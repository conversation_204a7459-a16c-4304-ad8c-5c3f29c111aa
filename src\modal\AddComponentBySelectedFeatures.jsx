import React, { useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { Button, Input } from 'antd';
import { getComponentRectInfoByFeatures } from '../viewer/util';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { useAddComponentMutation, useDeleteFeatureMutation, useLazyGetAllFeaturesQuery } from '../services/product';


const AddComponentBySelectedFeatures = (props) => {
  const {
    isOpened,
    setIsOpened,
    allFeatures,
    curSelectedRectsRef,
    curProduct,
    refetchAllComponents,
    refetchAllFeatures,
    updateAllFeaturesState,
    fcanvasRef,
    allComponents,
    refetchAggregatedReevaluationResult,
  } = props;

  const { t } = useTranslation();

  const [designator, setDesignator] = useState('');
  const [packageNo, setPackageNo] = useState('');
  const [partNo, setPartNo] = useState('');

  const [lazyGetAllFeatures] = useLazyGetAllFeaturesQuery();
  const [deleteFeature] = useDeleteFeatureMutation();
  const [addComponent] = useAddComponentMutation();

  const handleGroupSelectedFeatures = async (
    allFeatures,
    curProduct,
    designator,
    packageNo,
    partNo,
    allComponents,
  ) => {
    if (_.isEmpty(curSelectedRectsRef.current)) return;

    const fids = _.map(curSelectedRectsRef.current, r => r.get('fid'));
    const features = _.filter(allFeatures, f => _.includes(fids, f.feature_id));

    // for now add feature in non golden sub board is not allowed hence we can use the first subboard's added features
    const featuresInFirstSubBoard = _.filter(features, f => f.array_index === 0 || f.array_index === null);

    // console.log('features', features);

    const componentRectInfo = getComponentRectInfoByFeatures(featuresInFirstSubBoard);

    // console.log('componentRectInfo', componentRectInfo);

    // submit
    const payload = {
      definition_product_id: Number(_.get(curProduct, 'product_id', 0)),
      definition_step: 0,
      designator: designator,
      package_no: packageNo,
      part_no: partNo,
      center: componentRectInfo.center,
      feature_ids: fids,
      shape: {
        type: 'obb',
        points: [
          componentRectInfo.pMin,
          componentRectInfo.pMax,
        ],
        center: null,
        angle: 0,
      },
    };

    const res = await addComponent(payload);

    if (res.error) {
      aoiAlert(t('notification.error.addComponent'), ALERT_TYPES.COMMON_ERROR);
      console.error('add component failed', res.error.message);
      return;
    }

    fcanvasRef.current.discardActiveObject().renderAll();

    await refetchAllComponents();

    if (_.get(res, 'data.cloned', false)) {
      // also need to remove the features that created when drawn
      for (const f of features) {
        await deleteFeature({
          product_id: Number(_.get(curProduct, 'product_id', 0)),
          step: 0,
          feature_id: _.get(f, 'feature_id', 0),
        });
      }

      const res1 = await lazyGetAllFeatures({
        product_id: Number(_.get(curProduct, 'product_id', 0)),
        step: 0,
        marker: false,
        component_id: _.get(res, 'data.components[0].region_group_id', 0),
      });

      if (res1.error) {
        aoiAlert(t('notification.error.getAllFeatures'), ALERT_TYPES.COMMON_ERROR);
        console.error(res1.error.message);
        return;
      }

      await updateAllFeaturesState(_.map(features, f => f.feature_id), 'delete')
      await updateAllFeaturesState(_.map(res1.data, 'feature_id'), 'add', res1.data);
    } else {
      await updateAllFeaturesState(_.map(features, f => f.feature_id), 'update', _.map(features, f => (
        {
          ...f,
          group_id: _.get(res, 'data.components[0].region_group_id', null),
        }
      )));
    }

    await refetchAggregatedReevaluationResult();

    setDesignator('');
    setPackageNo('');
    setPartNo('');
    setIsOpened(false);
  };

  return (
    <CustomModal
      width={386}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%] whitespace-nowrap'>
        {t('productDefine.addComponent')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col self-stretch'>
        <div className='flex py-6 px-4 flex-col gap-8 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
              {t('productDefine.refDesignator')}
            </span>
            <Input
              size='small'
              style={{ width: '100%' }}
              value={designator}
              onChange={(e) => setDesignator(e.target.value)}
            />
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
              {t('productDefine.packageNo')}
            </span>
            <Input
              size='small'
              style={{ width: '100%' }}
              value={packageNo}
              onChange={(e) => setPackageNo(e.target.value)}
            />
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
              {t('productDefine.partNo')}
            </span>
            <Input
              size='small'
              style={{ width: '100%' }}
              value={partNo}
              onChange={(e) => setPartNo(e.target.value)}
            />
          </div>
        </div>
        <div className='flex p-4 gap-2 items-center flex-1 self-stretch'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{ width: '50%' }}
            onClick={() => {
              handleGroupSelectedFeatures(
                allFeatures,
                curProduct,
                designator,
                _.isEmpty(packageNo) ? null : packageNo,
                _.isEmpty(partNo) ? null : partNo,
                allComponents,
              );
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[150%]'>
              {t('productDefine.newComponent')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  )
};

export default AddComponentBySelectedFeatures;