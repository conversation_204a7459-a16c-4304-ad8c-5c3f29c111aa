import React, { useCallback, useEffect, useRef } from 'react';
import { generalPan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, generalPan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, generalPan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, generalPanZoomMouse<PERSON>heelHandler, generateFabricArrow, getComponentCenterByRoiDtoObj, loadHighResolScene, loadInitFullSizeThumbnail, rotatePoint, zoomPanToObject } from './util';
import { highResoluRefreshInterval, newRectStrokeWidth, serverHost } from '../common/const';
import { useLazyGetImageMetaDataQuery } from '../services/camera';
import _ from 'lodash';
import { fabric } from 'fabric';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setTransparentLoadingEnabled } from '../reducer/setting';
import { useTranslation } from 'react-i18next';
import { destroyFabricCanvas } from '../common/util';


const InspectionReviewFullViewer = (props) => {
  const {
    components,
    features, // in ipc view this would be the selected component's features
    isInspectedView,
    goldenProduct,
    selectedDCid,
    selectedFid,
    selectedArrayIndex,
  } = props;

  const dispatch = useDispatch();
  const { t } = useTranslation();

  const viewerContRef = useRef();
  const canvasElRef = useRef();
  const fcanvasRef = useRef();
  const isPanning = useRef(false);
  const thumbnailBgSceneRef = useRef();
  const displayedHighResolSceneRef = useRef();
  const componentRects = useRef([]);
  const featureRects = useRef([]);
  const selectedFeatureIndicator = useRef();

  const [lazyGetImageMetadata] = useLazyGetImageMetaDataQuery();

  const updateZIndex = () => {
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);
    if (componentRects.current.length > 0) componentRects.current.forEach((rect) => rect.moveTo(3));
    if (featureRects.current.length > 0) featureRects.current.forEach((rect) => rect.moveTo(4));
    if (selectedFeatureIndicator.current) selectedFeatureIndicator.current.moveTo(5);
    fcanvasRef.current.renderAll();
  };

  const loadFeatureIndicator = useCallback(
    _.debounce((selectedDCid, selectedFid, isInspectedView, selectedArrayIndex) => {
      if (!fcanvasRef.current) return;

      if (selectedFeatureIndicator.current) {
        fcanvasRef.current.remove(selectedFeatureIndicator.current);
        selectedFeatureIndicator.current = null;
      }

      const rect = _.find(featureRects.current, (r) => {
        const feature = r.get('featureObj');

        if (isInspectedView) {
          return _.get(feature, 'component_id', 0) === selectedDCid && _.get(feature, 'feature_id', 0) === selectedFid && _.get(feature, 'array_index', 0) === selectedArrayIndex;
        }
        return isInspectedView ? _.get(feature, 'component_id', 0) === selectedDCid && _.get(feature, 'feature_id', 0) === selectedFid && _.get(feature, 'array_index', 0) === selectedArrayIndex :
        _.get(feature, 'group_id', 0) === selectedDCid && _.get(feature, 'feature_id', 0) === selectedFid && _.get(feature, 'array_index', 0) === selectedArrayIndex;
      });
      if (!rect) return;

      // load component indicator
      const aabbBBox = rect.getBoundingRect(true);
      // get the center and dimension of the indicator based on the current zoom, the indicator should have a fixed size regardless of the zoom level
      // and the indicator should be above the component rect aabb bbox and pointing down
      const zoom = fcanvasRef.current.getZoom();
      const indicatorWidth = 40 / zoom;
      const indicatorHeight = 40 / zoom;
      const center = {
        x: aabbBBox.left + aabbBBox.width / 2,
        y: aabbBBox.top - indicatorHeight / 2,
      };
      selectedFeatureIndicator.current = generateFabricArrow({
        angleDeg: 180,
        center,
        dimension: {
          width: indicatorWidth,
          height: indicatorHeight,
        },
        color: '#EB5757',
      });
      selectedFeatureIndicator.current.set('selectable', false);
      selectedFeatureIndicator.current.set('evented', false);
      fcanvasRef.current.add(selectedFeatureIndicator.current);
    },
  highResoluRefreshInterval), []);

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  []);

  const init = async (components, features, isInspectedView, goldenProduct, selectedFid, selectedDCid, selectedArrayIndex) => {
    if (!fcanvasRef.current) return;

    // remove previous objects
    if (fcanvasRef.current) {
      if (thumbnailBgSceneRef.current) {
        fcanvasRef.current.remove(thumbnailBgSceneRef.current);
        thumbnailBgSceneRef.current = null;
      }
      if (displayedHighResolSceneRef.current) {
        fcanvasRef.current.remove(displayedHighResolSceneRef.current);
        displayedHighResolSceneRef.current = null;
      }
      if (componentRects.current.length > 0) {
        componentRects.current.forEach((rect) => fcanvasRef.current.remove(rect));
        componentRects.current = [];
      }
      if (featureRects.current.length > 0) {
        featureRects.current.forEach((rect) => fcanvasRef.current.remove(rect));
        featureRects.current = [];
      }
    }

    // load scene, component, and feature rects

    let curImageMetadata;
    if (!isInspectedView) {
      const metadataRes = await lazyGetImageMetadata({ uri: _.get(goldenProduct, 'inspectables[0].color_map_uri', '') });
        
      if (metadataRes.error) {
        console.error('Failed to get image metadata');
        return;
      }

      curImageMetadata = metadataRes.data;
    }

    // scene
    if (!isInspectedView) {
      await loadInitFullSizeThumbnail({
        fcanvas: fcanvasRef.current,
        rawWidth: _.get(curImageMetadata, 'width'),
        rawHeight: _.get(curImageMetadata, 'height'),
        thumbnailBgSceneRef,
        imageUri: _.get(goldenProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(goldenProduct, 'inspectables[0].depth_map_uri'),
        type: 'image',
      });

      thumbnailBgSceneRef.current.set('originalRectTopLeftWithZeroRotation', {
        left: 0,
        top: 0,
      });

      thumbnailBgSceneRef.current.set('originalRectInnerDim', {
        width: curImageMetadata.width,
        height: curImageMetadata.height,
      });

      zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0, 0);

      // load high res scene
      await delayLoadHighSoluScene({
        fcanvasRef,
        rawImageW: _.get(curImageMetadata, 'width', 0),
        rawImageH: _.get(curImageMetadata, 'height', 0),
        displayedHighResolSceneRef,
        imageUri: _.get(goldenProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(goldenProduct, 'inspectables[0].depth_map_uri'),
      });
    } else {
      const component = _.find(components, c => c.result_component_id === selectedDCid && c.array_index === selectedArrayIndex);
      if (!component) return;
      let url = `${serverHost}/blob?type=image`;
      url += `&color_uri=${encodeURIComponent(_.get(component, 'color_map_uri', ''))}`;
      url += `&depth_uri=${encodeURIComponent(_.get(component, 'depth_map_uri', ''))}`;

      const img = await new Promise((resolve, reject) => {
        fabric.util.loadImage(url, (img) => {
          resolve(img);
        });
      });

      thumbnailBgSceneRef.current = new fabric.Image(img, {
        selectable: false,
        evented: false,
        // top: _.get(components, '[0].shape.points[0].y', 0),
        // left: _.get(components, '[0].shape.points[0].x', 0),
        top: 0,
        left: 0,
      });

      thumbnailBgSceneRef.current.set('originalRectTopLeftWithZeroRotation', {
        // left: _.get(components, '[0].shape.points[0].x', 0),
        // top: _.get(components, '[0].shape.points[0].y', 0),
        left: 0,
        top: 0,
      });

      thumbnailBgSceneRef.current.set('originalRectInnerDim', {
        width: _.get(component, 'shape.points[1].x', 0) - _.get(component, 'shape.points[0].x', 0) + 1,
        height: _.get(component, 'shape.points[1].y', 0) - _.get(component, 'shape.points[0].y', 0) + 1,
      });

      // thumbnailBgSceneRef.current.rotate(_.get(component, 'shape.angle', 0));
      const thumbBounds = thumbnailBgSceneRef.current.getBoundingRect();

      fcanvasRef.current.add(thumbnailBgSceneRef.current);

      let zoom = Math.min(
        fcanvasRef.current.getWidth()/ thumbBounds.width,
        fcanvasRef.current.getHeight() / thumbBounds.height,
      );
    
      const center = {
        x: thumbBounds.left + thumbBounds.width / 2,
        y: thumbBounds.top + thumbBounds.height / 2,
      };

      fcanvasRef.current.zoomToPoint(center, zoom);
    
      const newRectCenter = fabric.util.transformPoint({
        x: thumbBounds.left + thumbBounds.width / 2,
        y: thumbBounds.top + thumbBounds.height / 2,
      }, fcanvasRef.current.viewportTransform);
    
      // Calculate the pan adjustment to center the cropped area
      const panX = (fcanvasRef.current.getWidth() / 2 - newRectCenter.x) + fcanvasRef.current.viewportTransform[4];
      const panY = (fcanvasRef.current.getHeight() / 2 - newRectCenter.y) + fcanvasRef.current.viewportTransform[5];
    
      // Apply the pan adjustment
      fcanvasRef.current.viewportTransform = [zoom, 0, 0, zoom, panX, panY];
    
      // Re-render the canvas
      fcanvasRef.current.requestRenderAll();
    }

    // component & feature rects
    if (!isInspectedView) {
      // load component for golden product viewer only
      // for (const c of components) {
      //   const rect = new fabric.Rect({
      //     left: _.get(c, 'shape.points[0].x', 0) - newRectStrokeWidth,
      //     top: _.get(c, 'shape.points[0].y', 0) - newRectStrokeWidth,
      //     width: _.get(c, 'shape.points[1].x', 0) - _.get(c, 'shape.points[0].x', 0) + 1 + newRectStrokeWidth,
      //     height: _.get(c, 'shape.points[1].y', 0) - _.get(c, 'shape.points[0].y', 0) + 1 + newRectStrokeWidth,
      //     fill: 'transparent',
      //     stroke: (_.get(c, 'region_group_id', 0) === selectedDCid && _.get(c, 'array_index', 0) === selectedArrayIndex) ? '#56CCF2' : 'white',
      //     strokeWidth: newRectStrokeWidth,
      //     selectable: false,
      //     evented: false,
      //     perPixelTargetFind: true,
      //   });

      //   rect.setControlsVisibility({
      //     mt: false,
      //     mb: false,
      //     ml: false,
      //     mr: false,
      //     bl: false,
      //     br: false,
      //     tl: false,
      //     tr: false,
      //     mtr: false,
      //   });

      //   rect.hoverCursor = 'default';

      //   if (_.get(c, 'shape.angle', 0) > 0) rect.rotate(_.get(c, 'shape.angle', 0));

      //   fcanvasRef.current.add(rect);
      //   componentRects.current.push(rect);
      // }

      // only load the selected component
      const c = _.find(components, c => c.region_group_id === selectedDCid && c.array_index === selectedArrayIndex);
      if (!c) return;
      const rect = new fabric.Rect({
        left: _.get(c, 'shape.points[0].x', 0) - newRectStrokeWidth,
        top: _.get(c, 'shape.points[0].y', 0) - newRectStrokeWidth,
        width: _.get(c, 'shape.points[1].x', 0) - _.get(c, 'shape.points[0].x', 0) + 1 + newRectStrokeWidth,
        height: _.get(c, 'shape.points[1].y', 0) - _.get(c, 'shape.points[0].y', 0) + 1 + newRectStrokeWidth,
        fill: 'transparent',
        stroke: 'white',
        strokeWidth: newRectStrokeWidth,
        selectable: false,
        evented: false,
        perPixelTargetFind: true,
      });

      rect.setControlsVisibility({
        mt: false,
        mb: false,
        ml: false,
        mr: false,
        bl: false,
        br: false,
        tl: false,
        tr: false,
        mtr: false,
      });

      rect.hoverCursor = 'default';

      if (_.get(c, 'shape.angle', 0) > 0) rect.rotate(_.get(c, 'shape.angle', 0));

      fcanvasRef.current.add(rect);
      componentRects.current.push(rect);
    }

    for (const f of features) {
      if ((!isInspectedView && _.get(f, 'group_id', 0) !== selectedDCid) || _.get(f, 'array_index', 0) !== selectedArrayIndex) continue;
      if (isInspectedView && _.get(f, 'component_id', 0) !== selectedDCid) continue;
      const component = isInspectedView ? _.find(components, c => c.component_id === selectedDCid) : _.find(components, c => c.region_group_id === f.group_id && c.array_index === selectedArrayIndex);

      // const componentCenter = {
      //   x: _.get(component, 'shape.points[0].x', 0) + (_.get(component, 'shape.points[1].x', 0) - _.get(component, 'shape.points[0].x', 0) + 1) / 2,
      //   y: _.get(component, 'shape.points[0].y', 0) + (_.get(component, 'shape.points[1].y', 0) - _.get(component, 'shape.points[0].y', 0) + 1) / 2,
      // };
      let componentCenter;

      if (isInspectedView) {
        componentCenter = {
          x: 0,
          y: 0,
        };
      } else {
        componentCenter = getComponentCenterByRoiDtoObj(_.get(component, 'shape', {}));
      }
      const featureCenter = {
        x: _.get(f, 'roi.points[0].x', 0) + (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
        y: _.get(f, 'roi.points[0].y', 0) + (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,
      };

      // const rotatedFeatureCenter = rotatePoint(featureCenter, _.get(component, 'shape.angle', 0), componentCenter);

      const rect = new fabric.Rect({
        left: featureCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2 - newRectStrokeWidth,
        top: featureCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2 - newRectStrokeWidth,
        width: _.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1 + newRectStrokeWidth,
        height: _.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1 + newRectStrokeWidth,
        fill: 'transparent',
        stroke: _.get(f, 'feature_id', 0) === selectedFid && _.get(f, 'array_index', 0) === selectedArrayIndex ? '#56CCF2' : 'white',
        strokeWidth: newRectStrokeWidth,
        selectable: false,
        evented: false,
        perPixelTargetFind: true,
      });

      rect.hoverCursor = 'default';
      rect.rotate(_.get(f, 'roi.angle', 0));

      rect.setControlsVisibility({
        mt: false,
        mb: false,
        ml: false,
        mr: false,
        bl: false,
        br: false,
        tl: false,
        tr: false,
        mtr: false,
      });

      rect.set('featureObj', f);

      fcanvasRef.current.add(rect);
      featureRects.current.push(rect);

      if (
        _.get(f, 'feature_id', 0) === selectedFid &&
        (
          (!isInspectedView && _.get(f, 'group_id', 0) === selectedDCid && _.get(f, 'array_index', 0) === selectedArrayIndex) ||
          (isInspectedView && _.get(f, 'component_id', 0) === selectedDCid && _.get(f, 'array_index', 0) === selectedArrayIndex)
        )
      ) {
        loadFeatureIndicator(selectedDCid, selectedFid, isInspectedView, selectedArrayIndex);
      }
    }
    
    updateZIndex();

    // attach mouse events
    // fcanvasRef.current.on('mouse:down', (opt) => {
    //   generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanning);
    // });
    // fcanvasRef.current.on('mouse:move', (opt) => {
    //   generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanning);
    // });
    // fcanvasRef.current.on('mouse:up', () => {
    //   generalPanZoomMouseUpHandler(fcanvasRef, isPanning);
    //   if (!isInspectedView) {
    //     loadHighResolScene({
    //       fcanvasRef,
    //       rawImageW: _.get(curImageMetadata, 'width', 0),
    //       rawImageH: _.get(curImageMetadata, 'height', 0),
    //       displayedHighResolSceneRef,
    //       imageUri: _.get(goldenProduct, 'inspectables[0].color_map_uri'),
    //       depthUri: _.get(goldenProduct, 'inspectables[0].depth_map_uri'),
    //       type: 'image',
    //       callback: () => {
    //         updateZIndex();
    //       },
    //     });
    //   }
    // });
    // fcanvasRef.current.on('mouse:wheel', (opt) => {
    //   generalPanZoomMouseWheelHandler(opt, fcanvasRef);
    //   if (!isInspectedView) {
    //     delayLoadHighSoluScene({
    //       fcanvasRef,
    //       rawImageW: _.get(curImageMetadata, 'width', 0),
    //       rawImageH: _.get(curImageMetadata, 'height', 0),
    //       displayedHighResolSceneRef,
    //       imageUri: _.get(goldenProduct, 'inspectables[0].color_map_uri'),
    //       depthUri: _.get(goldenProduct, 'inspectables[0].depth_map_uri'),
    //     });
    //     loadFeatureIndicator(selectedDCid, selectedFid, isInspectedView, selectedArrayIndex);
    //   }
    // });
  };

  useEffect(() => {
    if (_.isUndefined(components) || _.isUndefined(features) || _.isNull(selectedFid) || _.isNull(selectedDCid)) return;
    if (!isInspectedView && _.isUndefined(goldenProduct)) return;
    if (isInspectedView && (_.isEmpty(components) || _.isEmpty(features))) return;

    if (!viewerContRef.current || !canvasElRef.current) return;

    if (fcanvasRef.current) {
      fcanvasRef.current.dispose();
      fcanvasRef.current = null;
    }

    fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
      antialias: 'off',
      uniformScaling: false,
      selection: false,
    });

    fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);
    fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);

    init(components, features, isInspectedView, goldenProduct, selectedFid, selectedDCid, selectedArrayIndex);
  }, [components, features, goldenProduct, selectedFid, selectedDCid, selectedArrayIndex]);

  useEffect(() => {
    return () => {
      if (fcanvasRef.current) {
        destroyFabricCanvas(fcanvasRef.current);
        fcanvasRef.current = null;
      }
    };
  }, []);

  return (
    <div className='relative w-full h-full'>
      <div
        className='absolute top-0 left-0 w-full h-full'
        ref={viewerContRef}
      >
        <canvas ref={canvasElRef} />
      </div>
    </div>
  );
};

export default InspectionReviewFullViewer;