import React, { useCallback, useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { useGetImageMetaDataQuery } from '../services/camera';
import _ from 'lodash';
import { attachModifiedEventToSubArrayBoardSelectionRoi, filterComponentsBySelectionRoi, generalDrawRectMouseDownHandle, generalDrawRectMouseMoveHandle, generalPanZoomMouseDownHandler, generalPanZoomMouseMoveHandler, generalPanZoomMouseUpHandler, generalPanZoomMouseWheelHandler, loadHighResolScene, loadInitFullSizeThumbnail, zoomPanToObject, middlePanZoomMouseDownHandler, rotatePoint } from './util';
import { useDispatch } from 'react-redux';
import { setTransparentLoadingEnabled } from '../reducer/setting';
import { destroyFabricCanvas } from '../common/util';
import { defaultRoiColor, highResoluRefreshInterval, arrayBoardSelectionRoiStrokeWidth, newRectStrokeWidth } from '../common/const';
import { useGoldenRegisterArrayMutation, useDetectMarkerCenterMutation } from '../services/product';
import { useTranslation } from 'react-i18next';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { Button } from 'antd';


const PCBArrayViewer = (props) => {
  const {
    curProduct,
    selectedTool,
    allComponents,
    handleFinishDrawingSelectionRoi,
    arrayRegisteration,
    handleRefetchAllComponents,
    refetchArrayRegisteration,
    setCurSelectedMarker,
    curSelectedMarker,
    curMarkers,
    setSelectedTool,
    isSubBoardSelectionRoiDisplayed,
    panZoomToArrayBoardSelectionIdx,
    setPanZoomToArrayBoardSelectionIdx,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const containerRef = useRef(null);
  const canvasElRef = useRef(null);
  const fcanvasRef = useRef(null);
  const viewerContRef = useRef(null);
  const thumbnailBgSceneRef = useRef(null);
  const displayedHighResolSceneRef = useRef(null);
  const isPanningRef = useRef(false);
  const displayedComponentRoisRef = useRef([]);
  const currentSelectionRoi = useRef(null); // drawing
  const drawingInitMousePos = useRef(null); // drawing
  const subBoardSelectionRoiRef = useRef(null);
  const detectedMarkerCirclesRef = useRef({});

  const [selectionOptionPos, setSelectionOptionPos] = useState(null);
  const [hoveredSubBoardInfo, setHoveredSubBoardInfo] = useState(null);
  const hoveredSubBoardInfoRef = useRef(null);

  const { data: curImageMetadata } = useGetImageMetaDataQuery({ uri: _.get(curProduct, 'inspectables[0].color_map_uri') });
  const [goldenRegisterArray] = useGoldenRegisterArrayMutation();
  const [detectMarkerCenter] = useDetectMarkerCenterMutation();

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetadata]);

  const reloadAllComponents = (components) => {
    if (!fcanvasRef.current) return;

    if (!_.isEmpty(displayedComponentRoisRef.current)) {
      for (const roi of displayedComponentRoisRef.current) {
        fcanvasRef.current.remove(roi);
      }
      displayedComponentRoisRef.current = [];
    }

    if (_.isEmpty(components)) return;

    for (const c of components) {
      const pMin = _.get(c, 'shape.points[0]', 0);
      const pMax = _.get(c, 'shape.points[1]', 0);

      const rect = new fabric.Rect({
        left: pMin.x - arrayBoardSelectionRoiStrokeWidth,
        top: pMin.y - arrayBoardSelectionRoiStrokeWidth,
        width: pMax.x - pMin.x + 1 + arrayBoardSelectionRoiStrokeWidth,
        height: pMax.y - pMin.y + 1 + arrayBoardSelectionRoiStrokeWidth,
        fill: 'transparent',
        stroke: defaultRoiColor,
        strokeWidth: arrayBoardSelectionRoiStrokeWidth,
        selectable: false,
        evented: false,
        angle: 0,
      });

      rect.rotate(_.get(c, 'shape.angle', 0));

      rect.set('componentObj', c);
      rect.set('arrayIndex', _.get(c, 'array_index', 0));
      rect.set('originalAng', _.get(c, 'shape.angle', 0));
      rect.set('originalRectInnerDim', {
        width: pMax.x - pMin.x + 1,
        height: pMax.y - pMin.y + 1,
      });
      rect.set('originalRotatedCenter', rect.getCenterPoint());

      fcanvasRef.current.add(rect);
      displayedComponentRoisRef.current.push(rect);
    }

    updateZIndex();
  };

  const updateZIndex = () => {
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);
    let selectionRoiZIndex = 3;
    if (!_.isEmpty(displayedComponentRoisRef.current)) {
      for (const roi of displayedComponentRoisRef.current) {
        roi.moveTo(3);
        selectionRoiZIndex += 1;
      }
    }
    let curDrawningSelectionRoiZIndex = selectionRoiZIndex;
    if (!_.isEmpty(subBoardSelectionRoiRef.current)) {
      for (const roi of subBoardSelectionRoiRef.current) {
        roi.moveTo(selectionRoiZIndex);
        curDrawningSelectionRoiZIndex += 1;
      }
    }
    if (currentSelectionRoi.current) {
      currentSelectionRoi.current.moveTo(curDrawningSelectionRoiZIndex);
      curDrawningSelectionRoiZIndex += 1;
    }
    if (!_.isEmpty(detectedMarkerCirclesRef.current)) {
      _.forEach(detectedMarkerCirclesRef.current, circle => {
        circle.moveTo(curDrawningSelectionRoiZIndex);
        curDrawningSelectionRoiZIndex += 1;
      });
    }
    fcanvasRef.current.renderAll();
  };

  const panZoomToArrayBoardSelection = (idx) => {
    if (!fcanvasRef.current) return;
    const rect = _.find(subBoardSelectionRoiRef.current, r => r.get('arrayIndex') === idx);
    if (!rect) return;
    zoomPanToObject(rect, fcanvasRef.current, 0.01);

    _.forEach(fcanvasRef.current.getObjects(), (obj) => {
      obj.setCoords();
    });

    loadHighResolScene({
      fcanvasRef,
      rawImageW: _.get(curImageMetadata, 'width', 0),
      rawImageH: _.get(curImageMetadata, 'height', 0),
      displayedHighResolSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
      callback: () => {
        updateZIndex();
      },
    });
    setPanZoomToArrayBoardSelectionIdx(null);
  };

  const handleToolChange = (
    tool,
    allComponents,
    curProduct,
  ) => {
    if (!fcanvasRef.current) return;

    // Reset cursor to default
    fcanvasRef.current.defaultCursor = 'default';
    fcanvasRef.current.hoverCursor = 'move';

    fcanvasRef.current.discardActiveObject().renderAll();

    fcanvasRef.current.off('mouse:down');
    fcanvasRef.current.off('mouse:move');
    fcanvasRef.current.off('mouse:up');
    fcanvasRef.current.off('selection:created');
    fcanvasRef.current.off('selection:updated');
    setSelectionOptionPos(null);

    // if (!_.isEmpty(subBoardSelectionRoiRef.current)) {
    //   subBoardSelectionRoiRef.current.forEach(r => {
    //     r.set('evented', tool === 'select' && r.get('arrayIndex') !== 0)
    //     r.set('selectable', tool === 'select' && r.get('arrayIndex') !== 0);
    //   });
    // }

    if (tool === 'transform') {
      fcanvasRef.current.on('mouse:down', (opt) => {
        opt.e.preventDefault();
        if (opt.e.button === 0) generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanningRef);
        if (opt.e.button === 1) middlePanZoomMouseDownHandler(fcanvasRef, isPanningRef);
        setSelectionOptionPos(null);
      });
      fcanvasRef.current.on('mouse:move', (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanningRef));
      fcanvasRef.current.on('mouse:up', (opt) => {
        generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);

        const activeObj = fcanvasRef.current.getActiveObject();
        if (!_.isUndefined(activeObj) && !_.isNull(activeObj)) return;

        loadHighResolScene({
          fcanvasRef,
          rawImageW: _.get(curImageMetadata, 'width', 0),
          rawImageH: _.get(curImageMetadata, 'height', 0),
          displayedHighResolSceneRef,
          imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
          depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
          type: 'image',
          callback: () => {
            updateZIndex();
          },
        });
      });
    } else if (tool === 'select') {
      fcanvasRef.current.on('mouse:down', (opt) => {
        if (opt.e.button === 1) {
          middlePanZoomMouseDownHandler(fcanvasRef, isPanningRef);
        }
      });
      fcanvasRef.current.on('mouse:move', (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanningRef));
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 1) {
          generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);

          loadHighResolScene({
            fcanvasRef,
            rawImageW: _.get(curImageMetadata, 'width', 0),
            rawImageH: _.get(curImageMetadata, 'height', 0),
            displayedHighResolSceneRef,
            imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
            depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
            type: 'image',
            callback: () => {
              updateZIndex();
            },
          });
        }
      });

      // fcanvasRef.current.on('selection:created', () => {
      //   hoveredSubBoardInfoRef.current = null;
      //   setHoveredSubBoardInfo(null);
      // });
      // fcanvasRef.current.on('selection:updated', () => {
      //   hoveredSubBoardInfoRef.current = null;
      //   setHoveredSubBoardInfo(null);
      // });

    } else if (tool === 'selectMarker') {
      if (detectedMarkerCirclesRef.current.temp) {
        fcanvasRef.current.remove(detectedMarkerCirclesRef.current.temp);
        delete detectedMarkerCirclesRef.current.temp;
        updateZIndex();
      }
      fcanvasRef.current.on('mouse:down', (opt) => {
        opt.e.preventDefault();
        if (opt.e.button === 0) {
          generalDrawRectMouseDownHandle(opt, fcanvasRef.current, (rect) => {
            currentSelectionRoi.current = rect;
            fcanvasRef.current.add(rect);
            updateZIndex();
          });
          const pointer = fcanvasRef.current.getPointer(opt.e);
          drawingInitMousePos.current = { x: pointer.x, y: pointer.y };
        }
      });
      fcanvasRef.current.on('mouse:move', (opt) => {
        if (!currentSelectionRoi.current) return;
        generalDrawRectMouseMoveHandle(opt, fcanvasRef.current, currentSelectionRoi.current, drawingInitMousePos.current);
        updateZIndex();
      });
      fcanvasRef.current.on('mouse:up', async (opt) => {
        if (!currentSelectionRoi.current) return;
        if (opt.e.button === 0) {
          const roi = {
            type: 'obb',
            points: [
              {
                x: _.round(currentSelectionRoi.current.left + newRectStrokeWidth, 0),
                y: _.round(currentSelectionRoi.current.top + newRectStrokeWidth, 0),
              },
              {
                x: _.round(currentSelectionRoi.current.left + currentSelectionRoi.current.width - 1, 0),
                y: _.round(currentSelectionRoi.current.top + currentSelectionRoi.current.height - 1, 0),
              },
            ],
            angle: 0,
          };
          const res = await detectMarkerCenter({
            product_id: Number(_.get(curProduct, 'product_id', 0)),
            step: 0,
            roi,
            feature_type: 'marker',
            line_item_params: {
              aligner_2d: {
                name: 'aligner_2d',
                agent_name: 'aligner_2d',
                enabled: true,
                params: {
                  marker_type: {
                    param_enum: {
                      value: 'CIRCLE',
                      options: ['CIRCLE', 'RECTANGLE', 'TEMPLATE'],
                    },
                  },
                },
              },
            },
          });

          if (res.error) {
            aoiAlert(t('notification.error.detectMarkerCenter'), ALERT_TYPES.COMMON_ERROR);
            console.error('detectMarkerCenter error:', _.get(res, 'error.message', ''));
          } else if (res.data) {
            const responseType = _.get(res, 'data.type');

            if (responseType === 'CIRCLE' && _.get(res, 'data.circle')) {
              const marker = {
                x: _.round(_.get(res, 'data.circle.center.x', 0), 0),
                y: _.round(_.get(res, 'data.circle.center.y', 0), 0),
                radius: _.round(_.get(res, 'data.circle.radius', 0), 0),
              };
              setCurSelectedMarker(marker);

              if (detectedMarkerCirclesRef.current.temp) {
                fcanvasRef.current.remove(detectedMarkerCirclesRef.current.temp);
                delete detectedMarkerCirclesRef.current.temp;
              }
              detectedMarkerCirclesRef.current.temp = new fabric.Circle({
                left: marker.x,
                top: marker.y,
                originX: 'center',
                originY: 'center',
                radius: marker.radius || arrayBoardSelectionRoiStrokeWidth * 2,
                fill: 'rgba(255,0,0,0.5)',
                stroke: 'red',
                strokeWidth: arrayBoardSelectionRoiStrokeWidth,
                selectable: false,
                evented: false,
                strokeUniform: true,
              });
              fcanvasRef.current.add(detectedMarkerCirclesRef.current.temp);
              updateZIndex();
            }
          }

          fcanvasRef.current.remove(currentSelectionRoi.current);
          currentSelectionRoi.current = null;
          setSelectedTool('transform');
          updateZIndex();
        }
      });
    } else if (tool === 'defineSelectionRoi') {
      fcanvasRef.current.defaultCursor = 'crosshair';
      fcanvasRef.current.hoverCursor = 'crosshair';
      fcanvasRef.current.on('mouse:down', (opt) => {
        opt.e.preventDefault();
        if (opt.e.button === 0) {
          generalDrawRectMouseDownHandle(opt, fcanvasRef.current, (rect) => {
            currentSelectionRoi.current = rect;
            fcanvasRef.current.add(rect);
            updateZIndex();
          });
          const pointer = fcanvasRef.current.getPointer(opt.e);
          drawingInitMousePos.current = {
            x: pointer.x,
            y: pointer.y,
          };
        }
      });
      fcanvasRef.current.on('mouse:move', (opt) => {
        if (!currentSelectionRoi.current) return;
        generalDrawRectMouseMoveHandle(opt, fcanvasRef.current, currentSelectionRoi.current, drawingInitMousePos.current);
        updateZIndex();
      });
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (!currentSelectionRoi.current) return;
        if (opt.e.button === 0) {
          const filteredComponents = filterComponentsBySelectionRoi(allComponents, {
            pMin: {
              x: currentSelectionRoi.current.left + arrayBoardSelectionRoiStrokeWidth,
              y: currentSelectionRoi.current.top + arrayBoardSelectionRoiStrokeWidth,
            },
            pMax: {
              x: currentSelectionRoi.current.left + currentSelectionRoi.current.width,
              y: currentSelectionRoi.current.top + currentSelectionRoi.current.height,
            }
          });

          const rectCenter = currentSelectionRoi.current.getCenterPoint();

          handleFinishDrawingSelectionRoi(
            filteredComponents,
            Number(_.get(curProduct, 'product_id', 0)),
            0,
            [
              {
                array_index: 0,
                flip: {
                  x: false,
                  y: false,
                  center: {
                    x: rectCenter.x,
                    y: rectCenter.y,
                  }
                },
                rotation: {
                  angle: 0,
                  center: {
                    x: rectCenter.x,
                    y: rectCenter.y,
                  }
                },
                translation: {
                  x: 0,
                  y: 0,
                },
              }
            ],
            {
              type: 'obb',
              points: [
                {
                  x: _.round(currentSelectionRoi.current.left + arrayBoardSelectionRoiStrokeWidth, 0),
                  y: _.round(currentSelectionRoi.current.top + arrayBoardSelectionRoiStrokeWidth, 0),
                },
                {
                  x: _.round(currentSelectionRoi.current.left + currentSelectionRoi.current.width - 1, 0),
                  y: _.round(currentSelectionRoi.current.top + currentSelectionRoi.current.height - 1, 0),
                }
              ],
              angle: 0,
            },
          );

          fcanvasRef.current.remove(currentSelectionRoi.current);
          currentSelectionRoi.current = null;

          setSelectedTool('transform');

          updateZIndex();
        }
      });
    }

    updateZIndex();
  };

  const reloadSubBoardSelectionRoi = (arrayRegisteration, step, curProduct, isSubBoardSelectionRoiDisplayed, selectedTool) => {
    // console.log('reloadSubBoardSelectionRoi', arrayRegisteration, step, curProduct, isSubBoardSelectionRoiDisplayed);

    const { array_transforms: arrayTransforms, selection_roi: selectionRoi } = arrayRegisteration;
    // no need to worry about components under sub board's transformation of the selection
    // this is already applied to the component dto's shape by backend

    if (!fcanvasRef.current) return;

    // Clear existing sub-board selection ROIs
    if (subBoardSelectionRoiRef.current) {
      if (Array.isArray(subBoardSelectionRoiRef.current)) {
        subBoardSelectionRoiRef.current.forEach(roi => {
          fcanvasRef.current.remove(roi);
        });
      } else {
        fcanvasRef.current.remove(subBoardSelectionRoiRef.current);
      }
      fcanvasRef.current.renderAll();
      subBoardSelectionRoiRef.current = null;
    }

    if (!isSubBoardSelectionRoiDisplayed) return;

    if (!arrayTransforms || !Array.isArray(arrayTransforms) || !selectionRoi) {
      return;
    }

    const selectionPMin = _.get(selectionRoi, 'points[0]', null);
    const selectionPMax = _.get(selectionRoi, 'points[1]', null);

    if (!selectionPMin || !selectionPMax) {
      return;
    }

    // Calculate selection ROI dimensions
    const selectionWidth = selectionPMax.x - selectionPMin.x + 1;
    const selectionHeight = selectionPMax.y - selectionPMin.y + 1;

    // Create array to store all sub-board ROI rectangles
    const subBoardRois = [];

    // Create a rectangle for each array transform
    arrayTransforms.forEach((transform) => {
      const { rotation, translation, array_index } = transform;
      const rotationAngle = _.get(rotation, 'angle', 0);
      const translationX = _.get(translation, 'x', 0);
      const translationY = _.get(translation, 'y', 0);

      // Calculate the center of the selection ROI in its local coordinate system
      const localSelectionCenter = {
        x: selectionWidth / 2,
        y: selectionHeight / 2
      };

      // Apply translation (translation is based on selectionRoi's pMin in local coord)
      const translatedCenter = {
        x: selectionPMin.x + translationX + localSelectionCenter.x,
        y: selectionPMin.y + translationY + localSelectionCenter.y
      };

      // Create fabric rectangle
      const rect = new fabric.Rect({
        left: translatedCenter.x - selectionWidth / 2 - arrayBoardSelectionRoiStrokeWidth,
        top: translatedCenter.y - selectionHeight / 2 - arrayBoardSelectionRoiStrokeWidth,
        width: selectionWidth + arrayBoardSelectionRoiStrokeWidth,
        height: selectionHeight + arrayBoardSelectionRoiStrokeWidth,
        fill: 'transparent',
        stroke: defaultRoiColor,
        strokeWidth: arrayBoardSelectionRoiStrokeWidth,
        // selectable: array_index !== 0 && selectedTool === 'select',
        // evented: array_index !== 0 && selectedTool === 'select',
        selectable: true,
        evented: true,
        angle: 0,
      });

      rect.set('arrayIndex', array_index);
      rect.set('xFilped', _.get(transform, 'flip.x', false));
      rect.set('yFilped', _.get(transform, 'flip.y', false));

      if (array_index === 0) rect.hoverCursor = 'disabled';

      // Apply rotation around the rectangle's center
      if (rotationAngle !== 0) {
        rect.rotate(rotationAngle);
      }

      rect.set('originalAng', rotationAngle);
      rect.set('originalRectInnerDim', {
        width: selectionWidth,
        height: selectionHeight,
      });
      rect.set('originalRotatedCenter', rect.getCenterPoint());

      if (array_index !== 0) {
        // attach modified event
        attachModifiedEventToSubArrayBoardSelectionRoi(
          rect,
          arrayRegisteration,
          step,
          curProduct,
          goldenRegisterArray,
          handleRefetchAllComponents,
          refetchArrayRegisteration,
          t,
        );
      }

      rect.on('mouseover', (opt) => {
        if (!fcanvasRef.current) return;
        if (fcanvasRef.current.getActiveObject()) return;
        const rectBox = containerRef.current.getBoundingClientRect();
        const info = {
          index: array_index,
          x: opt.e.clientX - rectBox.left,
          y: opt.e.clientY - rectBox.top,
        };
        hoveredSubBoardInfoRef.current = info;
        setHoveredSubBoardInfo(info);
      });

      rect.on('mousemove', (opt) => {
        if (!hoveredSubBoardInfoRef.current) return;
        if (hoveredSubBoardInfoRef.current.index !== array_index) return;
        const rectBox = containerRef.current.getBoundingClientRect();
        hoveredSubBoardInfoRef.current = {
          ...hoveredSubBoardInfoRef.current,
          x: opt.e.clientX - rectBox.left,
          y: opt.e.clientY - rectBox.top,
        };
        setHoveredSubBoardInfo({ ...hoveredSubBoardInfoRef.current });
      });

      if (array_index !== 0 && selectedTool === 'select') {
        rect.setControlsVisibility({
          mt: false,
          mb: false,
          ml: false,
          mr: false,
          bl: false,
          br: false,
          tl: false,
          tr: false,
        });

        rect.on('moving', () => {
          const components = displayedComponentRoisRef.current.filter(r => _.get(r.get('componentObj'), 'array_index') === array_index);
          const delta = {
            x: rect.getCenterPoint().x - rect.get('originalRotatedCenter').x,
            y: rect.getCenterPoint().y - rect.get('originalRotatedCenter').y,
          };

          for (const cRect of components) {
            cRect.set({
              left: cRect.get('originalRotatedCenter').x + delta.x - cRect.get('originalRectInnerDim').width / 2 - arrayBoardSelectionRoiStrokeWidth,
              top: cRect.get('originalRotatedCenter').y + delta.y - cRect.get('originalRectInnerDim').height / 2 - arrayBoardSelectionRoiStrokeWidth,
              angle: 0,
            });
            cRect.setCoords();
            cRect.rotate(cRect.get('originalAng'));
          }

          fcanvasRef.current.renderAll();
        });

        rect.on('rotating', () => {
          const components = displayedComponentRoisRef.current.filter(r => _.get(r.get('componentObj'), 'array_index') === array_index);
          const rotationDelta = rect.angle - rect.get('originalAng');
          const refCenter = rect.get('originalRotatedCenter');

          for (const cRect of components) {
            const cCenter = rotatePoint(cRect.get('originalRotatedCenter'), rotationDelta, refCenter);
            cRect.set({
              left: cCenter.x - cRect.get('originalRectInnerDim').width / 2 - arrayBoardSelectionRoiStrokeWidth,
              top: cCenter.y - cRect.get('originalRectInnerDim').height / 2 - arrayBoardSelectionRoiStrokeWidth,
              angle: 0,
            });
            cRect.setCoords();
            cRect.rotate(cRect.get('originalAng') + rotationDelta);
          }

          fcanvasRef.current.renderAll();
        });

        rect.on('mouseup', (opt) => {
          if (opt.e.button === 2) {
            opt.e.preventDefault();
            // right click show options for the selected region
            setSelectionOptionPos({
              x: opt.e.clientX - containerRef.current.getBoundingClientRect().left,
              y: opt.e.clientY - containerRef.current.getBoundingClientRect().top,
              type: 'subBoard',
              selectedArrayIndex: array_index,
            });

            loadHighResolScene({
              fcanvasRef,
              rawImageW: _.get(curImageMetadata, 'width', 0),
              rawImageH: _.get(curImageMetadata, 'height', 0),
              displayedHighResolSceneRef,
              imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
              depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
              type: 'image',
              callback: () => {
                updateZIndex();
              },
            });
          }
        });

        rect.on('mouseout', () => {
          hoveredSubBoardInfoRef.current = null;
          setHoveredSubBoardInfo(null);
        });

        rect.on('mousedown', () => {
          hoveredSubBoardInfoRef.current = null;
          setHoveredSubBoardInfo(null);
        });
      } else {
        rect.setControlsVisibility({
          mt: false,
          mb: false,
          ml: false,
          mr: false,
          bl: false,
          br: false,
          tl: false,
          tr: false,
          mtr: false,
        });
        rect.lockMovementX = true;
        rect.lockMovementY = true;
        rect.hoverCursor = 'default';
        rect.selectable = false;

        rect.on('mousedown', (opt) => {
          opt.e.preventDefault();
          if (opt.e.button === 0) generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanningRef);
          if (opt.e.button === 1) middlePanZoomMouseDownHandler(fcanvasRef, isPanningRef);
          setSelectionOptionPos(null);
        });
        rect.on('mousemove', (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanningRef));
        rect.on('mouseup', (opt) => {
          if (opt.e.button === 0) {
            generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);

            const activeObj = fcanvasRef.current.getActiveObject();
            if (!_.isUndefined(activeObj) && !_.isNull(activeObj)) return;

            loadHighResolScene({
              fcanvasRef,
              rawImageW: _.get(curImageMetadata, 'width', 0),
              rawImageH: _.get(curImageMetadata, 'height', 0),
              displayedHighResolSceneRef,
              imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
              depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
              type: 'image',
              callback: () => {
                updateZIndex();
              },
            });
          } else if (opt.e.button === 1) {
            generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);
            setSelectionOptionPos(null);
            loadHighResolScene({
              fcanvasRef,
              rawImageW: _.get(curImageMetadata, 'width', 0),
              rawImageH: _.get(curImageMetadata, 'height', 0),
              displayedHighResolSceneRef,
              imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
              depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
              type: 'image',
              callback: () => {
                updateZIndex();
              },
            });
          }
        });

        rect.on('mouseout', () => {
          hoveredSubBoardInfoRef.current = null;
          setHoveredSubBoardInfo(null);
        });

        rect.on('mousedown', () => {
          hoveredSubBoardInfoRef.current = null;
          setHoveredSubBoardInfo(null);
        });
      }

      // Add to canvas and store reference
      fcanvasRef.current.add(rect);
      subBoardRois.push(rect);
    });

    // Store all sub-board ROIs in the ref
    subBoardSelectionRoiRef.current = subBoardRois;

    // Update z-index to ensure proper layering
    updateZIndex();
  };

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;
    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };

  const init = async (curProduct, curImageMetadata) => {
    await loadInitFullSizeThumbnail({
      fcanvas: fcanvasRef.current,
      rawWidth: _.get(curImageMetadata, 'width'),
      rawHeight: _.get(curImageMetadata, 'height'),
      thumbnailBgSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
    });

    await loadHighResolScene({
      fcanvasRef,
      rawImageH: _.get(curImageMetadata, 'height', 0),
      rawImageW: _.get(curImageMetadata, 'width', 0),
      displayedHighResolSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
    });

    resetView();
  };

  useEffect(() => {
    if (!_.isInteger(panZoomToArrayBoardSelectionIdx)) return;
    panZoomToArrayBoardSelection(panZoomToArrayBoardSelectionIdx);
    setPanZoomToArrayBoardSelectionIdx(null);
  }, [panZoomToArrayBoardSelectionIdx]);

  useEffect(() => {
    // console.log('arrayRegisteration', arrayRegisteration);
    // console.log('fcanvasRef.current', fcanvasRef.current);
    if (!fcanvasRef.current) return;

    if (_.isEmpty(_.get(arrayRegisteration, 'array_transforms', [])) || _.isEmpty(_.get(arrayRegisteration, 'selection_roi', {}))) return;

    reloadSubBoardSelectionRoi(
      arrayRegisteration,
      0,
      curProduct,
      isSubBoardSelectionRoiDisplayed,
      selectedTool,
    );
  }, [arrayRegisteration, isSubBoardSelectionRoiDisplayed, selectedTool]);

  useEffect(() => {
    reloadAllComponents(allComponents);
  }, [allComponents]);

  useEffect(() => {
    if (!fcanvasRef.current) return;

    _.forEach(detectedMarkerCirclesRef.current, (circle, key) => {
      if (key === 'temp') return;
      fcanvasRef.current.remove(circle);
    });
    _.forEach(Object.keys(detectedMarkerCirclesRef.current), key => {
      if (key !== 'temp') delete detectedMarkerCirclesRef.current[key];
    });

    _.forEach(curMarkers, (marker, key) => {
      if (marker && _.isNumber(marker.x) && _.isNumber(marker.y)) {
        const circle = new fabric.Circle({
          left: marker.x,
          top: marker.y,
          originX: 'center',
          originY: 'center',
          radius: _.get(marker, 'radius', arrayBoardSelectionRoiStrokeWidth * 2),
          fill: 'rgba(255,0,0,0.5)',
          stroke: 'red',
          strokeWidth: arrayBoardSelectionRoiStrokeWidth,
          selectable: false,
          evented: false,
          strokeUniform: true,
        });
        fcanvasRef.current.add(circle);
        detectedMarkerCirclesRef.current[key] = circle;
      }
    });

    updateZIndex();
  }, [curMarkers]);

  useEffect(() => {
    if (!fcanvasRef.current) return;

    if (detectedMarkerCirclesRef.current.temp) {
      fcanvasRef.current.remove(detectedMarkerCirclesRef.current.temp);
      delete detectedMarkerCirclesRef.current.temp;
    }

    if (curSelectedMarker && _.isNumber(curSelectedMarker.x) && _.isNumber(curSelectedMarker.y)) {
      detectedMarkerCirclesRef.current.temp = new fabric.Circle({
        left: curSelectedMarker.x,
        top: curSelectedMarker.y,
        originX: 'center',
        originY: 'center',
        radius: _.get(curSelectedMarker, 'radius', arrayBoardSelectionRoiStrokeWidth * 2),
        fill: 'rgba(255,0,0,0.5)',
        stroke: 'red',
        strokeWidth: arrayBoardSelectionRoiStrokeWidth,
        selectable: false,
        evented: false,
        strokeUniform: true,
      });
      fcanvasRef.current.add(detectedMarkerCirclesRef.current.temp);
    }

    updateZIndex();
  }, [curSelectedMarker]);

  useEffect(() => {
    handleToolChange(
      selectedTool,
      allComponents,
      curProduct,
    );
  }, [selectedTool]);

  useEffect(() => {
    const handler = (e) => {
      if (!hoveredSubBoardInfoRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      hoveredSubBoardInfoRef.current = {
        ...hoveredSubBoardInfoRef.current,
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      };
      setHoveredSubBoardInfo({ ...hoveredSubBoardInfoRef.current });
    };

    if (viewerContRef.current) {
      viewerContRef.current.addEventListener('mousemove', handler);
    }

    return () => {
      if (viewerContRef.current) {
        viewerContRef.current.removeEventListener('mousemove', handler);
      }
    };
  }, []);

  useEffect(() => {
    if (_.isUndefined(curImageMetadata) || _.isUndefined(curProduct) || !canvasElRef.current || !viewerContRef.current) return;

    if (!fcanvasRef.current) {
      fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
        fireRightClick: true,
        stopContextMenu: true,
        preserveObjectStacking: true,
        fireMiddleClick: true,
      });
    }

    fcanvasRef.current.on('mouse:wheel', (opt) => {
      generalPanZoomMouseWheelHandler(opt, fcanvasRef);
      delayLoadHighSoluScene({
        fcanvasRef,
        rawImageW: _.get(curImageMetadata, 'width', 0),
        rawImageH: _.get(curImageMetadata, 'height', 0),
        displayedHighResolSceneRef,
        imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      });
    });

    fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);
    fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);

    init(curProduct, curImageMetadata);

    handleToolChange(
      selectedTool,
      allComponents,
      curProduct,
    );

    if (!_.isEmpty(_.get(arrayRegisteration, 'array_transforms', [])) && !_.isEmpty(_.get(arrayRegisteration, 'selection_roi', {}))) {
      reloadSubBoardSelectionRoi(
        arrayRegisteration,
        0,
        curProduct,
        isSubBoardSelectionRoiDisplayed,
        selectedTool,
      );
    }
  }, [curImageMetadata]);

  useEffect(() => {
    return () => {
      if (fcanvasRef.current) {
        destroyFabricCanvas(fcanvasRef.current);
        fcanvasRef.current = null;
      }
    };
  }, []);

  return (
    <div
      className='relative w-full h-full'
      ref={containerRef}
    >
      {!_.isEmpty(selectionOptionPos) &&
        <div className='absolute z-[20]'
          style={{
            display: _.isEmpty(selectionOptionPos) ? 'none' : 'inline-flex',
            top: `${selectionOptionPos.y + 5}px`,
            left: `${selectionOptionPos.x + 5}px`,
            borderRadius: '2px',
            background: '#1E1E1E',
            padding: '8px',
            flexDirection: 'column',
            gap: '4px',
          }}
        >
          <Button
            style={{ width: '100%' }}
            type='text'
            onClick={() => {
              const payload = {
                product_id: Number(_.get(curProduct, 'product_id', 0)),
                step: 0,
                selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
                component_ids: _.get(arrayRegisteration, 'component_ids', []),
              };
              payload.array_transforms = _.map(_.get(arrayRegisteration, 'array_transforms', []), t => {
                if (t.array_index === selectionOptionPos.selectedArrayIndex) {
                  return {
                    ...t,
                    flip: {
                      ...t.flip,
                      x: !t.flip.x,
                    },
                  };
                }
                return t;
              });

              const submit = async (payload) => {
                const res = await goldenRegisterArray(payload);

                if (res.error) {
                  aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
                  console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
                  return;
                }

                await refetchArrayRegisteration();
                await handleRefetchAllComponents(
                  payload.product_id,
                  0,
                );
              };

              submit(payload);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
              {t('productDefine.flipX')}
            </span>
          </Button>
          <Button
            style={{ width: '100%' }}
            type='text'
            onClick={() => {
              const payload = {
                product_id: Number(_.get(curProduct, 'product_id', 0)),
                step: 0,
                selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
                component_ids: _.get(arrayRegisteration, 'component_ids', []),
              };
              payload.array_transforms = _.map(_.get(arrayRegisteration, 'array_transforms', []), t => {
                if (t.array_index === selectionOptionPos.selectedArrayIndex) {
                  return {
                    ...t,
                    flip: {
                      ...t.flip,
                      y: !t.flip.y,
                    },
                  };
                }
                return t;
              });

              const submit = async (payload) => {
                const res = await goldenRegisterArray(payload);

                if (res.error) {
                  aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
                  console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
                  return;
                }

                await refetchArrayRegisteration();
                await handleRefetchAllComponents(
                  payload.product_id,
                  0,
                );
              };

              submit(payload);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
              {t('productDefine.flipY')}
            </span>
          </Button>
          <Button
            style={{ width: '100%' }}
            type='text'
            onClick={() => {
              const payload = {
                product_id: Number(_.get(curProduct, 'product_id', 0)),
                step: 0,
                selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
                component_ids: _.get(arrayRegisteration, 'component_ids', []),
              };
              payload.array_transforms = _.map(_.get(arrayRegisteration, 'array_transforms', []), t => {
                if (t.array_index === selectionOptionPos.selectedArrayIndex) {
                  return {
                    ...t,
                    rotation: {
                      ...t.rotation,
                      angle: t.rotation.angle - 90,
                    },
                  };
                }
                return t;
              });

              const submit = async (payload) => {
                const res = await goldenRegisterArray(payload);

                if (res.error) {
                  aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
                  console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
                  return;
                }

                await refetchArrayRegisteration();
                await handleRefetchAllComponents(
                  payload.product_id,
                  0,
                );
              };

              submit(payload);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
              {t('productDefine.rotate90Left')}
            </span>
          </Button>
          <Button
            style={{ width: '100%' }}
            type='text'
            onClick={() => {
              const payload = {
                product_id: Number(_.get(curProduct, 'product_id', 0)),
                step: 0,
                selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
                component_ids: _.get(arrayRegisteration, 'component_ids', []),
              };
              payload.array_transforms = _.map(_.get(arrayRegisteration, 'array_transforms', []), t => {
                if (t.array_index === selectionOptionPos.selectedArrayIndex) {
                  return {
                    ...t,
                    rotation: {
                      ...t.rotation,
                      angle: t.rotation.angle + 90,
                    },
                  };
                }
                return t;
              });

              const submit = async (payload) => {
                const res = await goldenRegisterArray(payload);

                if (res.error) {
                  aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
                  console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
                  return;
                }

                await refetchArrayRegisteration();
                await handleRefetchAllComponents(
                  payload.product_id,
                  0,
                );
              };

              submit(payload);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
              {t('productDefine.rotate90Right')}
            </span>
          </Button>
          <Button
            style={{ width: '100%' }}
            type='text'
            onClick={() => {
              const payload = {
                product_id: Number(_.get(curProduct, 'product_id', 0)),
                step: 0,
                selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
                component_ids: _.get(arrayRegisteration, 'component_ids', []),
              };
              payload.array_transforms = _.map(_.get(arrayRegisteration, 'array_transforms', []), t => {
                if (t.array_index === selectionOptionPos.selectedArrayIndex) {
                  return {
                    ...t,
                    rotation: {
                      ...t.rotation,
                      angle: t.rotation.angle + 180,
                    },
                  };
                }
                return t;
              });

              const submit = async (payload) => {
                const res = await goldenRegisterArray(payload);

                if (res.error) {
                  aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
                  console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
                  return;
                }

                await refetchArrayRegisteration();
                await handleRefetchAllComponents(
                  payload.product_id,
                  0,
                );
              };

              submit(payload);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
              {t('productDefine.rotate180')}
            </span>
          </Button>
          <Button
            style={{ width: '100%' }}
            type='text'
            onClick={() => {
              setSelectionOptionPos(null);
            }}
          >
            <div className='flex items-center flex-1 justify-start gap-1'>
              <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                {t('common.close')}
              </span>
            </div>
          </Button>
        </div>
      }
      {hoveredSubBoardInfo &&
        <div
          className='absolute z-[20] pointer-events-none text-white text-xs bg-black bg-opacity-70 px-1 rounded'
          style={{
            top: `${hoveredSubBoardInfo.y}px`,
            left: `${hoveredSubBoardInfo.x + 10}px`,
          }}
        >
          {`${t('productDefine.arrayBoard')}-${hoveredSubBoardInfo.index + 1}`}
        </div>
      }
      <div
        className='absolute w-full h-full z-[10] top-0 left-0'
        ref={viewerContRef}
      >
        <canvas
          ref={canvasElRef}
        />
      </div>
    </div>
  );
};

export default PCBArrayViewer;