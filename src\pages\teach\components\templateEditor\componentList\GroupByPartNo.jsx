import { Config<PERSON><PERSON><PERSON>, Spin } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { CustomCollapse, CustomFeatureCollapse } from '../../../../../common/styledComponent';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { leadFeatureType, leadGapFeatureType, leadInspection2DBase, textVerification } from '../../../../../common/const';


const GroupByPartNo = (props) => {
  const {
    filteredComponents,
    allFeatures,
    selectedPartNo,
    setSelectedPartNo,
    allPartsCollapseRef,
    setSelectedFeatureType,
    selectedFeatureType,
    selectedCid,
    setSelectedCid,
    setRequiredLocateRect,
    setSelectedPackageNo,
    setSelectedScope,
    setSelectedFid,
    setSelectedAgentParam,
    setSelectedUngroupedFid,
    selectedUngroupedFid,
    selectedArrayIndex,
    setSelectedArrayIndex,
    aggregatedReevaluationResult,
  } = props;

  const { t } = useTranslation();

  const [parsedMap, setParsedMap] = useState({}); // { 'ex. ${partNo} / ${cid}': { label: '${partNo} / ${designator}', scope: 'part or component', featureTypesMap: { mounting: { mounting_2d: ... } } } }
  const [selectedLabel, setSelectedLabel] = useState(null);
  const [parsedAggregateResult, setParsedAggregateResult] = useState({});
  
  useEffect(() => {
    if (_.isInteger(selectedUngroupedFid)) setSelectedLabel(null);
  }, [selectedUngroupedFid]);

  useEffect(() => {
    if (_.isEmpty(aggregatedReevaluationResult)) {
      setParsedAggregateResult({});
      return;
    }

    const newMap = {};
    for (const r of aggregatedReevaluationResult) {
      if (!_.isEmpty(r.package_no)) {
        newMap[`${r.package_no}`] = r;
      }
      if (!_.isEmpty(r.part_no)) {
        newMap[`${r.part_no}`] = r;
      }
      newMap[`${r.component_id}`] = r;
    }

    setParsedAggregateResult(newMap);
  }, [aggregatedReevaluationResult]);

  useEffect(() => {
    // all component group by list higharchy is group by item -> feature type
    let newMap = {};
    let cidToFeatureTypeLineItemParamsMap = {}; // cid -> feature type -> line item params

    // first get each component's line item params config
    for (const f of allFeatures) {
      if (_.isInteger(f.group_id)) {
        if (!_.has(cidToFeatureTypeLineItemParamsMap, [f.group_id])) {
          cidToFeatureTypeLineItemParamsMap[`${f.group_id}`] = {
            // line_item_params: f.line_item_params || {},
            // feature_types: [f.feature_type],
            [`${f.feature_type}`]: f.line_item_params || {},
          };
        } else {
          if (_.includes(_.keys(cidToFeatureTypeLineItemParamsMap[`${f.group_id}`]), f.feature_type)) continue;
          cidToFeatureTypeLineItemParamsMap[`${f.group_id}`][`${f.feature_type}`] = f.line_item_params || {};
        }
      }
    }

    // console.log('cidToFeatureTypeLineItemParamsMap', cidToFeatureTypeLineItemParamsMap);

    for (const c of filteredComponents) {
      if (!_.has(cidToFeatureTypeLineItemParamsMap, [String(c.region_group_id)])) continue;
      if (!_.isEmpty(c.part_no) && c.can_group_by_part_no === true) {
        // part no group defined and linked to the part no group
        if (_.has(newMap, [c.part_no])) {
          newMap = _.set(newMap, [c.part_no], {
            ...newMap[`${c.part_no}`],
            healthy: c.healthy,
            featureTypesMap: {
              ..._.get(newMap, [c.part_no, 'featureTypesMap'], {}),
              ...cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            },
          });
        } else {
          newMap[`${c.part_no}`] = {
            scope: 'part',
            partNo: c.part_no,
            featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            healthy: c.healthy,
            cid: c.region_group_id,
            arrayIndex: c.array_index,
          };
        }
      } else if (!_.isEmpty(c.part_no)) {
        // part no defined but not linked to the part no group
        newMap[`${c.part_no} / ${c.region_group_id}`] = {
          label: `${c.part_no} / ${c.designator}`,
          scope: 'component',
          cid: c.region_group_id,
          featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
          healthy: c.healthy,
          partNo: c.part_no,
          arrayIndex: c.array_index,
        };
      } else if (_.isEmpty(c.part_no)) {
        // part no not defined so we don't care if it is linked to the part no group or not
        newMap[`${t('common.unknownPartNo')} / ${c.region_group_id}`] = {
          label: `${t('common.unknownPartNo')} / ${c.designator}`,
          scope: 'component',
          cid: c.region_group_id,
          featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
          healthy: c.healthy,
          partNo: c.part_no,
          arrayIndex: c.array_index,
        };
      }
    }

    // console.log('newMap', newMap);
    setParsedMap(newMap);
  }, [
    filteredComponents,
    allFeatures,
  ]);

  useEffect(() => {
    if (!_.isInteger(selectedCid)) return;

    // need to find out if it's component-part connected or not
    const c = _.find(filteredComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);
    if (!c) return;

    let label;
    if (!_.isEmpty(selectedPartNo) && c.can_group_by_part_no === true) {
      label = `${selectedPartNo}`;
      setSelectedScope('part');
    } else {
      label = `${_.isEmpty(selectedPartNo) ? t('common.unknownPartNo') : selectedPartNo} / ${c.region_group_id}`;
      setSelectedScope('component');
    }
    setSelectedLabel(label);
  }, [
    filteredComponents,
    selectedCid,
    selectedPartNo,
    selectedArrayIndex,
  ]);

  return (
    <Fragment>
      <ConfigProvider
        theme={{
          components: {
            Collapse: {
              headerPadding: '0px 0 0px 8px',
              contentPadding: '0 0 0 8px',
            }
          }
        }}
      >
        <CustomCollapse
          ref={allPartsCollapseRef}
          style={{ width: '100%' }}
          onChange={(keys) => {
            let label;
            setSelectedFeatureType(null);
            setSelectedPackageNo(null);
            setSelectedFid(null);
            setSelectedUngroupedFid(null);
            setSelectedAgentParam(null);

            if (_.isEmpty(keys)) {
              setSelectedPartNo(null);
              setSelectedCid(null);
              setSelectedLabel(null);
              setSelectedScope(null);
            } else if (keys.length === 1) {
              label = keys[0];
            } else {
              label = keys[1];
            }

            if (label) {
              setSelectedLabel(label);
              setSelectedScope(_.get(parsedMap, [label, 'scope'], null));
              if (_.get(parsedMap, [label, 'scope']) === 'part') {
                setSelectedCid(_.get(parsedMap, [label, 'cid'], null));
                setSelectedPartNo(_.get(parsedMap, [label, 'partNo'], null));
                setRequiredLocateRect({
                  cid: _.get(parsedMap, [label, 'cid'], null),
                  fid: null,
                });
                // setSelectedArrayIndex(_.get(parsedMap, [label, 'arrayIndex'], null));
                if (_.isInteger(_.get(parsedMap, [label, 'arrayIndex'], null))) {
                  setSelectedArrayIndex(0);
                } else {
                  setSelectedArrayIndex(null);
                }
              } else if (_.get(parsedMap, [label, 'scope']) === 'component') {
                // setSelectedPartNo(null);
                !_.isEmpty(_.get(parsedMap, [label, 'partNo'], null)) ? setSelectedPartNo(_.get(parsedMap, [label, 'partNo'], null)) : setSelectedPartNo(null);
                setSelectedCid(_.get(parsedMap, [label, 'cid'], null));
                setRequiredLocateRect({
                  cid: _.get(parsedMap, [label, 'cid'], null),
                  fid: null,
                });
                // setSelectedArrayIndex(_.get(parsedMap, [label, 'arrayIndex'], null));
                if (_.isInteger(_.get(parsedMap, [label, 'arrayIndex'], null))) {
                  setSelectedArrayIndex(0);
                } else {
                  setSelectedArrayIndex(null);
                }
              }
            }
          }}
          activeKey={_.isNull(selectedLabel) ? [] : [String(selectedLabel)]}
          items={_.map(_.keys(parsedMap), (labelKey) => {
            const partNoGroupObj = _.get(parsedMap, [labelKey], {});
            const displayLabel = _.get(partNoGroupObj, 'label', labelKey);
            return {
              key: labelKey,
              label: <div className={`flex h-[32px] px-2 items-center gap-2 justify-between`}>
                {(() => {
                  const isHealthy = _.get(partNoGroupObj, 'healthy', false);
                  if (!isHealthy) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                            {displayLabel}
                          </span>
                        </div>
                        <div
                          className='flex flex-col justify-center items-center gap-2.5 shrink-0 cursor-pointer'
                          title={t('productDefine.confirmComponentInfo')}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleConfirmUnhealthyPartNoGroup(partNoGroupObj);
                          }}
                        >
                          <img
                            src='/icn/unknown_gray.svg'
                            className='w-[12px] h-[12px]'
                            alt='unknown'
                          />
                        </div>
                      </Fragment>
                    );
                  }

                  const key = _.get(partNoGroupObj, 'scope') === 'part' ? _.get(partNoGroupObj, 'partNo') : _.get(partNoGroupObj, 'cid');

                  const numFail = _.get(parsedAggregateResult, [key, 'num_failing'], 0);
                  const numPass = _.get(parsedAggregateResult, [key, 'num_passing'], 0);

                  const containsWhite = _.get(parsedAggregateResult, [key, 'contains_white_feature'], false);

                  // console.log('containsWhite', containsWhite);

                  if (numFail === 0 && containsWhite && numPass > 0) {
                    return (
                      <div className='flex items-center gap-1'>
                        <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                          {displayLabel}
                        </span>
                      </div>
                    );
                  }

                  if (numFail === 0 && !containsWhite && numPass > 0) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[#57F2C4]`}>
                          {displayLabel}
                          </span>
                        </div>
                        <img src={'/icn/checkFilledCircle_green.svg'} className='w-[12px] h-[12px]' alt='status' />
                      </Fragment>
                    );
                  }

                  if (numFail > 0) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[#EB5E28]`}>
                          {displayLabel}
                          </span>
                        </div>
                        <img src={'/icn/failedCircled_red.svg'} className='w-[12px] h-[12px]' alt='status' />
                      </Fragment>
                    );
                  }

                  return (
                    <div className='flex items-center gap-1'>
                      <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                        {displayLabel}
                      </span>
                    </div>
                  );
                })()}
              </div>,
              children:
                <ConfigProvider
                  theme={{
                    components: {
                      Collapse: {
                        headerPadding: '0 0 0 8px',
                        contentPadding: '0 0 0 8px',
                      }
                    }
                  }}
                >
                  <CustomFeatureCollapse
                    style={{ width: '100%' }}
                    onChange={(keys) => {
                      setSelectedUngroupedFid(null);
                      setSelectedAgentParam(null);

                      let featureType;

                      if (_.isEmpty(keys)) {
                        setSelectedFeatureType(null);
                      } else if (keys.length === 1) {
                        setSelectedFeatureType(keys[0]);
                        featureType = keys[0];
                      } else {
                        setSelectedFeatureType(keys[1]);
                        featureType = keys[1];
                      }

                      featureType = _.startsWith(featureType, '_ic_lead') ? '_ic_lead' : featureType;

                      let sampleFid;

                      if (_.isInteger(selectedCid)) {
                        sampleFid = _.get(
                          _.find(allFeatures, f => f.feature_type === featureType && f.group_id === selectedCid && f.array_index === selectedArrayIndex),
                          'feature_id',
                        );

                        setSelectedFid(sampleFid);
                        setRequiredLocateRect({
                          cid: selectedCid,
                          fid: sampleFid,
                        });
                      }
                    }}
                    expandIcon={() => null}
                    activeKey={_.isNull(selectedFeatureType) ? [] : [selectedFeatureType]}
                    items={_.reduce(
                      _.keys(_.get(partNoGroupObj, 'featureTypesMap', {})),
                      (acc, type) => {
                        let numFail;
                        let numPass;
                        let containsWhite;

                        const key = _.get(partNoGroupObj, 'scope') === 'part' ? _.get(partNoGroupObj, 'partNo') : _.get(partNoGroupObj, 'cid');
                        // const lineItemResults = _.get(productDataSet, 'line_item_results', []);

                        if (!_.includes([leadFeatureType, leadGapFeatureType], type)) {
                          numFail = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type}`, 'num_failing'], 0);
                          numPass = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type}`, 'num_passing'], 0);
                          containsWhite = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type}`, 'contains_white_feature'], false);
                        } else {
                          const key = _.get(partNoGroupObj, 'scope') === 'part' ? _.get(partNoGroupObj, 'partNo') : _.get(partNoGroupObj, 'cid');
                          numFail = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type === leadFeatureType ? '_ic_lead' : '_ic_lead_gap'}`, 'num_failing'], 0);
                          numPass = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type === leadFeatureType ? '_ic_lead' : '_ic_lead_gap'}`, 'num_passing'], 0);
                          containsWhite = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type}`, 'contains_white_feature'], false);
                        }

                        const color = numFail > 0 ? '#EB5E28' : numPass > 0 && !containsWhite ? '#57F2C4' : '#fff';
                        const icon = numFail > 0 ? '/icn/failedCircled_red.svg' : numPass > 0 && !containsWhite ? '/icn/checkFilledCircle_green.svg' : '';

                        if (type === leadFeatureType) {
                          const leadCount = _.get(partNoGroupObj, `featureTypesMap._ic_lead.${leadInspection2DBase}.params.lead_count.param_int.value`, 0);

                          if (leadCount > 1) {
                            return acc.concat([
                              {
                                key: leadFeatureType,
                                label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                  <span
                                    className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis
                                    whitespace-nowrap`}
                                    style={{ color }}
                                  >
                                    {t('leadFeatureTypeText._ic_lead')}
                                  </span>
                                  {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                </div>,
                              },
                              {
                                key: leadGapFeatureType,
                                label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                  <span
                                    className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis
                                    whitespace-nowrap`}
                                    style={{ color }}
                                  >
                                    {t('leadFeatureTypeText._ic_lead_gap')}
                                  </span>
                                  {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                </div>,
                              }
                            ]);
                          } else {
                            return acc.concat([
                              {
                                key: leadFeatureType,
                                label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                  <span
                                    className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis
                                    whitespace-nowrap`}
                                    style={{ color }}
                                  >
                                    {t('leadFeatureTypeText._ic_lead')}
                                  </span>
                                  {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                </div>,
                              },
                            ]);
                          }
                        }

                        if (_.startsWith(type, '_text')) {
                          return acc.concat([{
                            key: type,
                            label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                              {(() => {
                                return (
                                  <Fragment>
                                    <div className='flex items-center gap-1'>
                                      <span className={`font-source text-[12px] font-normal leading-[150%]`} style={{ color }}>
                                        {t('leadFeatureTypeText._text')}
                                      </span>
                                      <span
                                        className='font-source text-[12px] font-normal leading-[150%] max-w-[60px] overflow-hidden overflow-ellipsis whitespace-nowrap'
                                        style={{ color }}
                                        title={_.get(partNoGroupObj, [
                                          'featureTypesMap',
                                          type,
                                          textVerification,
                                          'params',
                                          'expected_text',
                                          'param_string',
                                        ], '')}
                                      >
                                        ({_.get(partNoGroupObj, [
                                          'featureTypesMap',
                                          type,
                                          textVerification,
                                          'params',
                                          'expected_text',
                                          'param_string',
                                        ], '')})
                                      </span>
                                      <span className='font-source text-[12px] font-normal leading-[150%]' style={{ color }}>
                                      /
                                      </span>
                                      <span
                                        className='font-source text-[12px] font-normal leading-[150%] max-w-[60px] overflow-hidden overflow-ellipsis whitespace-nowrap'
                                        style={{ color }}
                                        title={_.get(partNoGroupObj, [
                                          'featureTypesMap',
                                          type,
                                          textVerification,
                                          'params',
                                          'expected_text_reverse',
                                          'param_string',
                                        ], '')}
                                      >
                                        ({_.get(partNoGroupObj, [
                                          'featureTypesMap',
                                          type,
                                          textVerification,
                                          'params',
                                          'expected_text_reverse',
                                          'param_string',
                                        ], '')})
                                      </span>
                                    </div>
                                    {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                  </Fragment>
                                );
                              })()}
                            </div>,
                          }]);
                        }

                        return acc.concat([{
                          key: type,
                          label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                            {(() => {
                              return (
                                <Fragment>
                                  <div className='flex items-center gap-1'>
                                    <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`} style={{ color }}>
                                      {t(`leadFeatureTypeText.${type.startsWith('_text') ? '_text': type}`)}
                                    </span>
                                  </div>
                                  {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                </Fragment>
                              );
                            })()}
                          </div>,
                        }]);
                      },
                      [],
                    )}
                  />
                </ConfigProvider>
            };
          })}
        />
      </ConfigProvider>
    </Fragment>
  );
};

export default GroupByPartNo;