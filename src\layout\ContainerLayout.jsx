import React, { Fragment, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import ContainerLoadingWithMessage from '../loader/ContainerLoaderWithMessage';
import { localStorageKeys, modelTypes, retrainModelTaskPhaseType, serverHost } from '../common/const';
import { setContainerLvlLoadingMsg, setCurTrainingTaskStartTime, setIsAgentParamRegenRunning, setIsAutoProgramTriggeredFromTemplateEditor, setIsContainerLvlLoadingEnabled, setIsTrainingRunning, setShouldRunReevaluateAfterRetrain, setTemplateEditorShouldRefetchNotifier } from '../reducer/setting';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { checkIfReadyForRetrain, sleep } from '../common/util';
import _ from 'lodash';
import ContainerTransparentLoader from '../loader/ContainerTransparentLoader';
import GlobalRetrainReminder from '../modal/GlobalRetrainReminder';
import { store } from '../store';
import { useModelUpdateTriggerMutation, useReevaluateExamplesMutation } from '../services/inference';


const ContainerLayout = (props) => {
  if (
    window.location.pathname === '/login' ||
    window.location.pathname === '/signup' ||
    window.location.pathname === '/landing-video'
  ) return props.children;

  const loopCheckRetrainStatus = useRef(null);

  const navigate = useNavigate();

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [reevaluate] = useReevaluateExamplesMutation();
  const [retrainTrigger] = useModelUpdateTriggerMutation();

  const isTrainingRuning = useSelector(state => state.setting.isTrainingRunning);
  const curProgrammingProductName = useSelector(state => state.setting.curProgrammingProductName);

  const initFetchTrainingStatusPolling = async () => {
    if (loopCheckRetrainStatus.current) {
      clearInterval(loopCheckRetrainStatus.current);
      loopCheckRetrainStatus.current = null;
    }

    loopCheckRetrainStatus.current = setInterval(async () => {
      let modelStatusRes;
      try {
        modelStatusRes = await fetch(`${serverHost}/getModelUpdates`, {
          method: 'GET',
          headers: {
            'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
          },
        });
      } catch (error) {
        console.error('Failed to fetch model status');
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }
      const modelStatus = await modelStatusRes.json();
      if (checkIfReadyForRetrain(modelStatus)) {
        const isAgentParamReGen = _.get(store.getState(), 'setting.isAgentParamRegenRunning', false);

        // const latestRetainFinishTimeByModelType = getLatestRetrainFinishTime(modelStatus);
        // dispatch(setLatestRetrainFinishTimeByModelType(latestRetainFinishTimeByModelType));
        if (
          !(_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.shouldRun', false) &&
          _.isInteger(_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.productId', 0)) &&
          isAgentParamReGen)
        ) {
          clearInterval(loopCheckRetrainStatus.current);
          loopCheckRetrainStatus.current = null;
          dispatch(setIsContainerLvlLoadingEnabled(false));
          dispatch(setContainerLvlLoadingMsg(''));
          dispatch(setIsTrainingRunning(false));
          dispatch(setCurTrainingTaskStartTime(null));
        }

        // search for the task with greatest schedule_id
        const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
        if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid, retrainModelTaskPhaseType.partialFailure], _.get(latestTask, 'phase'))) {
          if (_.includes([retrainModelTaskPhaseType.partialFailure], _.get(latestTask, 'phase'))) {
            aoiAlert(t(`notification.error.partialRetrainFailed`), ALERT_TYPES.COMMON_ERROR);
          } else {
            aoiAlert(t(`notification.error.${isAgentParamReGen ? 'agentParamRegen' : 'retrainFailed'}`), ALERT_TYPES.COMMON_ERROR);
          }
          clearInterval(loopCheckRetrainStatus.current);
          loopCheckRetrainStatus.current = null;
          dispatch(setIsContainerLvlLoadingEnabled(false));
          dispatch(setContainerLvlLoadingMsg(''));
          dispatch(setIsTrainingRunning(false));
          dispatch(setCurTrainingTaskStartTime(null));
          return;
        }

        aoiAlert(t(`notification.success.${isAgentParamReGen ? 'agentParamRegen' : 'retrainFinished'}`), ALERT_TYPES.COMMON_INFO);

        if (
          !_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.shouldRun', false) ||
          !_.isInteger(_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.productId', 0))
        ) {
          return;
        }

        if (_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.shouldRun', false) &&
        _.isInteger(_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.productId', 0))) {
          if (isAgentParamReGen) {
            // we need to retrain first before we can reevaluate
            dispatch(setIsContainerLvlLoadingEnabled(true));
            dispatch(setContainerLvlLoadingMsg(t('loader.modelTraining')));
            dispatch(setIsAgentParamRegenRunning(false));

            const res = await retrainTrigger({
              model_types: [
                modelTypes.mountingModel,
                modelTypes.leadModel,
                modelTypes.textVerificationModel,
                // modelTypes.textDirectionModel,
              ],
              golden_product_id: Number(_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.productId', 0)),
              update_parameters: false
            });

            await sleep(3 * 1000); // ensure task schuedule is written in db

            if (res.error) {
              aoiAlert(t('notification.error.retrainModel'), ALERT_TYPES.COMMON_ERROR);
              clearInterval(loopCheckRetrainStatus.current);
              loopCheckRetrainStatus.current = null;
              dispatch(setIsContainerLvlLoadingEnabled(false));
              dispatch(setContainerLvlLoadingMsg(''));
              dispatch(setIsTrainingRunning(false));
              dispatch(setCurTrainingTaskStartTime(null));
              return;
            }
          } else {
            clearInterval(loopCheckRetrainStatus.current);
            loopCheckRetrainStatus.current = null;

            dispatch(setIsContainerLvlLoadingEnabled(true));
            dispatch(setContainerLvlLoadingMsg(t('loader.reevaluatingAllExamples')));
            const res = await reevaluate({
              golden_product_id: Number(_.get(store.getState(), 'setting.shouldRunReevaluateAfterRetrain.productId', 0)),
              step: 0,
              has_feedback: true,
            });

            if (res.error) {
              aoiAlert(t('notification.error.reevaluateExample'), ALERT_TYPES.COMMON_ERROR);
            }

            dispatch(setIsContainerLvlLoadingEnabled(false));
            dispatch(setContainerLvlLoadingMsg(''));
            dispatch(setShouldRunReevaluateAfterRetrain(null));
            dispatch(setIsTrainingRunning(false));
            dispatch(setCurTrainingTaskStartTime(null));

            if (_.get(store.getState(), 'setting.isAutoProgramTriggeredFromTemplateEditor', false)) {
              dispatch(setTemplateEditorShouldRefetchNotifier(_.get(store.getState(), 'setting.templateEditorShouldRefetchNotifier', 0) + 1));
              dispatch(setIsAutoProgramTriggeredFromTemplateEditor(false));
            }
          }
        }

        return;
      }
    }, 1000);
  };

  useEffect(() => {
    if (!isTrainingRuning) {
      if (loopCheckRetrainStatus.current) {
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
      }
    } else {
      // init fetch training polling
      initFetchTrainingStatusPolling();
    }
  }, [isTrainingRuning]);

  return (
    <Fragment>
      <GlobalRetrainReminder />
      <div className='relative w-full h-full'>
        <ContainerLoadingWithMessage />
        <ContainerTransparentLoader />
        <div className='absolute top-0 left-0 w-full h-full z-[10]'>
          <div className='flex flex-col h-full w-full self-stretch'>
            <div
              className='flex h-[48px] py-1 px-6 justify-between items-center self-stretch'
              style={{ background: 'linear-gradient(0deg, #1E1E1E 0%, #1E1E1E 100%), rgba(255, 255, 255, 0.10)' }}
            >
              <div className='flex items-center flex-1 gap-[48px]'>
                <div className='flex gap-0.5 items-center'>
                  <div
                    className='flex gap-2 items-center cursor-pointer rounded-[4px] hover:bg-[#ffffff0d] transition-all duration-300 ease-in-out p-2'
                    onClick={() => {
                      navigate('/home');
                    }}
                  >
                    <div
                      className='flex h-8 w-8 items-center justify-center'
                    >
                      <img src='/icn/logo_white.svg' alt='logo' className='h-6 w-6' />
                    </div>
                    <span className='font-sansation text-[16px] font-normal tracking-[-0.32px] whitespace-nowrap'>
                      {t('common.daoaiAOISystem')}
                    </span>
                  </div>
                  {_.includes(['/settings', '/worklist'], window.location.pathname) && 
                    <div className='flex gap-0.5 items-center justify-center px-3 rounded-[4px] bg-[#57f2c433]'>
                      <span className='font-source text-[16px] font-semibold leading-[normal] text-[#57F2C4]'>
                        {window.location.pathname === '/settings' && t('allUpperCases.settings')}
                        {window.location.pathname === '/worklist' && t('allUpperCases.worklist')}
                      </span>
                    </div>
                  }
                  {window.location.pathname.includes('/teach') && !_.isEmpty(curProgrammingProductName) &&
                    <span className='font-source text-[14px] font-normal leading-[normal]'>
                      {`/ ${curProgrammingProductName}`}
                    </span>
                  }
                </div>
              </div>
              <div className='flex items-center gap-6 self-stretch'>
                <div className='flex h-8 w-8 items-center justify-center cursor-pointer'>
                  <img src='/icn/bell_white.svg' alt='bell' className='h-[14px] w-[11.5px]' />
                </div>
                { window.location.pathname !== '/settings' && (
                  <div
                    className='flex h-8 w-8 items-center justify-center cursor-pointer'
                    onClick={() => navigate('/settings')}
                  >
                    <img src='/icn/gear_white.svg' alt='gear' className='h-[16px] w-[15.15px]' />
                  </div>
                )}
                <div className='flex h-8 w-8 items-center justify-center cursor-pointer'>
                  <img src='/icn/info_white.svg' alt='info' className='h-[16px] w-[16px]' />
                </div>
                <div className='flex px-2 gap-1 self-stretch items-center'>
                  <span className='font-source text-[14px] font-normal'>
                    {localStorage.getItem(localStorageKeys.username) || ''}
                  </span>
                </div>
              </div>
            </div>
            <div className='flex flex-1 self-stretch'>
              {props.children}
            </div>
            <div
              className='flex py-1 px-6 items-center justify-center gap-4 self-stretch'
              style={{ background: 'linear-gradient(0deg, #1E1E1E 0%, #1E1E1E 100%), rgba(255, 255, 255, 0.10)' }}
            >
              <div className='flex gap-1 items-center'>
                <span className='font-source text-[12px] font-normal'>
                  {t('common.systemStatus')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default ContainerLayout;