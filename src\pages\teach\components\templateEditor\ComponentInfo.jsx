import { Button, Collapse, ConfigProvider, Input, Switch } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { useAddFeatureMutation, useAddFeatureRoiMutation, useApplyGroupLinkActionMutation, useApplyPartNoLinkActionMutation, useDeleteFeatureMutation, useLazyGetSampleComponentQuery, useUpdateComponentMutation } from '../../../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { getComponentCenterByRoiDtoObj, rotatePoint } from '../../../../viewer/util';
import { removeChineseChar, strContainsChineseChar } from '../../../../common/util';


const ComponentInfo = (props) => {
  const {
    allComponents,
    selectedCid,
    setIsAddFromLibraryOpened,
    setIsDrawModeEnabled,
    allFeatures,
    refetchAllComponents,
    selectedPackageNo,
    selectedPartNo,
    selectedScope,
    goldenProductId,
    updateAllFeaturesState,
    handleRefetchSelectedGroupAgentParams,
    selectedFeatureType,
    curProduct,
  } = props;

  const { t } = useTranslation();

  const [isEditingInfo, setIsEditingInfo] = useState(false);
  const [userDefinedDesignator, setUserDefinedDesignator] = useState('');
  const [userDefinedPackageNo, setUserDefinedPackageNo] = useState('');
  const [userDefinedPartNo, setUserDefinedPartNo] = useState('');
  const [isComponentPartNoLinked, setIsComponentPartNoLinked] = useState(false);
  const [isPartNoPackageNoLinked, setIsPartNoPackageNoLinked] = useState(false);
  const [isPartNoUnknown, setIsPartNoUnknown] = useState(false);
  const [isPackageNoUnknown, setIsPackageNoUnknown] = useState(false);
  // const [selectedComponent, setSelectedComponent] = useState(null);

  const [updateComponent] = useUpdateComponentMutation();
  const [applyGroupLinkAction] = useApplyGroupLinkActionMutation();
  const [applyPartNoLinkAction] = useApplyPartNoLinkActionMutation();
  const [deleteFeature] = useDeleteFeatureMutation();
  const [lazyGetSampleComponent] = useLazyGetSampleComponentQuery();
  const [addFeature] = useAddFeatureMutation();
  const [addFeatureRoi] = useAddFeatureRoiMutation();

  const handleSaveComponentInfo = async (
    userDefinedDesignator,
    userDefinedPackageNo,
    userDefinedPartNo,
    selectedScope,
    selectedCid,
    allComponents,
    allFeatures,
    goldenProductId,
  ) => {
    if (selectedScope !== 'component') return;

    const selectedComponent = _.find(allComponents, c => c.region_group_id === selectedCid);
    if (!selectedComponent) return;

    const payload = {
      ...selectedComponent,
      designator: userDefinedDesignator,
      package_no: userDefinedPackageNo,
      part_no: userDefinedPartNo,
    };

    delete payload['color_map_uri'];
    delete payload['depth_map_uri'];
    delete payload['created_at'];
    delete payload['modified_at'];
    delete payload['can_group_by_package_no'];
    delete payload['can_group_by_part_no'];
    delete payload['array_index'];
    delete payload['cloned'];
    delete payload['variation_for'];

    const res = await updateComponent({
      body: payload,
      params: { allComponents: false },
    });

    if (res.error) {
      aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      return;
    }

    await refetchAllComponents();

    aoiAlert(t('notification.success.updateComponent'), ALERT_TYPES.COMMON_INFO);
    return;

    // deprecated on 2025/07/14 for now just update the component info but with allComponents: false is enough
    // similar to replace template but in this case we apply changes to this single component not a group
    // 1 delete existing features
    // 2 generate new features payload
    // 3 add new features
    // 4 update component

    // const selectedComponent = _.find(allComponents, c => c.region_group_id === selectedCid);

    // let getSampleComponentPayload = {
    //   definition_product_id: selectedComponent.definition_product_id,
    //   definition_step: selectedComponent.definition_step,
    // };

    // if (!_.isEmpty(userDefinedPackageNo)) {
    //   getSampleComponentPayload.package_no = userDefinedPackageNo;
    // } else if (!_.isEmpty(userDefinedPartNo)) {
    //   getSampleComponentPayload.part_no = userDefinedPartNo;
    // }

    // const sampleComponentRes = await lazyGetSampleComponent(getSampleComponentPayload);

    // if (sampleComponentRes.error) {
    //   aoiAlert(t('notification.error.getComponent'), ALERT_TYPES.COMMON_ERROR);
    //   console.error('getSampleComponent error:', _.get(sampleComponentRes, 'error.message', ''));
    //   return;
    // }

    // const sampleComponent = _.find(allComponents, c => c.region_group_id === _.get(sampleComponentRes, 'data[0].region_group_id', null));

    // if (!sampleComponent) return;

    // const sampleFeatures = _.filter(allFeatures, f => f.group_id === sampleComponent.region_group_id);

    // const sourceFeatures = _.filter(allFeatures, f => f.group_id === selectedCid);

    // const sampleCenter = getComponentCenterByRoiDtoObj(_.get(sampleComponent, 'shape', {}));
    // const sourceCenter = getComponentCenterByRoiDtoObj(_.get(selectedComponent, 'shape', {}));

    // const translationDelta = {
    //   x: sourceCenter.x - sampleCenter.x,
    //   y: sourceCenter.y - sampleCenter.y,
    // };

    // if (sourceFeatures) {
    //   const effectedFeatures = [];
    //   for (const f of sourceFeatures) {
    //     const res = await deleteFeature({
    //       product_id: Number(goldenProductId),
    //       step: 0,
    //       feature_id: _.get(f, 'feature_id', 0),
    //     });
    //     if (res.error) {
    //       console.error('delete feature failed', res.error.message);
    //       aoiAlert(t('notification.error.deleteFeature'), ALERT_TYPES.COMMON_ERROR);
    //       return;
    //     }
    //     effectedFeatures.push(res.data);
    //   }
    //   await updateAllFeaturesState(_.map(sourceFeatures, 'feature_id'), 'delete');
    // }

    // const newFeatureObjs = [];
    // for (const f of sampleFeatures) {
    //   let fCenter = getComponentCenterByRoiDtoObj(_.get(f, 'roi', {}));
    //   fCenter = rotatePoint(fCenter, -_.get(sampleComponent, 'shape.angle', 0), sampleCenter);

    //   const payload = {
    //     ...f,
    //     group_id: selectedComponent.region_group_id,
    //     roi: {
    //       points: [
    //         {
    //           x: fCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2 + translationDelta.x,
    //           y: fCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2 + translationDelta.y,
    //         },
    //         {
    //           x: fCenter.x + (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2 + translationDelta.x,
    //           y: fCenter.y + (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2 + translationDelta.y,
    //         }
    //       ],
    //       angle: 0,
    //       center: null,
    //       type: 'obb',
    //     },
    //   };

    //   delete payload['feature_id'];

    //   const res = await addFeatureRoi({ body: payload, params: { allComponents: false } });

    //   if (res.error) {
    //     console.error('add feature failed', res.error.message);
    //     aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
    //     return;
    //   }

    //   newFeatureObjs.push(..._.map(res.data, f => ({
    //     ...f,
    //     line_item_params: payload.line_item_params,
    //   })));
    // }

    // await updateAllFeaturesState(_.map(newFeatureObjs, 'feature_id'), 'add', newFeatureObjs);

    // const payload = {
    //   ...selectedComponent,
    //   designator: userDefinedDesignator,
    //   package_no: userDefinedPackageNo,
    //   part_no: userDefinedPartNo,
    //   shape: {
    //     angle: 0,
    //     points: [
    //       {
    //         x: _.get(sampleComponent, 'shape.points[0].x', 0) + translationDelta.x,
    //         y: _.get(sampleComponent, 'shape.points[0].y', 0) + translationDelta.y,
    //       },
    //       {
    //         x: _.get(sampleComponent, 'shape.points[1].x', 0) + translationDelta.x,
    //         y: _.get(sampleComponent, 'shape.points[1].y', 0) + translationDelta.y,
    //       }
    //     ],
    //     center: null,
    //     type: 'obb',
    //   },
    //   feature_ids: _.map(newFeatureObjs, 'feature_id'),
    //   // all: false, // 2025/06/25 update component package/part no should be applied to single component
    // };

    // delete payload['color_map_uri'];
    // delete payload['depth_map_uri'];
    // delete payload['created_at'];
    // delete payload['modified_at'];
    // delete payload['can_group_by_part_no'];
    // delete payload['can_group_by_package_no'];
    // delete payload['array_index'];
    // delete payload['cloned'];
    // delete payload['variation_for'];
    // // delete payload['designator'];

    // const res = await updateComponent({
    //   body: payload,
    //   params: { allComponents: false },
    // });

    // if (res.error) {
    //   console.error('update component info failed', res.error.message);
    //   aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    // await refetchAllComponents();

    // aoiAlert(t('notification.success.updateComponent'), ALERT_TYPES.COMMON_INFO);
    // return;
  };

  const handleLinkingAction = async (
    action,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    allComponents,
    selectedFeatureType,
  ) => {
    const payload = {
      definition_product_id: Number(goldenProductId),
      definition_step: 0,
    };

    let res;

    if (_.isInteger(selectedCid) && selectedScope === 'component') {
      payload.component_id = selectedCid;
      const c = _.find(allComponents, c => c.region_group_id === selectedCid);
      if (!c) return;
      if (_.isEmpty(c.part_no) && !_.isEmpty(c.package_no)) {
        payload.can_group_by_package_no = action === 'link'; // in this case we are linking the component to the package since the part no is empty
        res = await applyPartNoLinkAction(payload);
      } else {
        payload.can_group_by_part_no = action === 'link';
        res = await applyGroupLinkAction(payload);
      }
    } else if (!_.isEmpty(selectedPartNo)) {
      payload.part_no = selectedPartNo;
      payload.can_group_by_package_no = action === 'link';
      res = await applyPartNoLinkAction(payload);
    } else if (!_.isEmpty(selectedPackageNo)) {
      // payload.package_no = selectedPackageNo;
      return;
    }

    if (res.error) {
      console.error('apply group link action failed', res.error.message);
      aoiAlert(t('notification.error.applyGroupLinkAction'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    await refetchAllComponents();
    // also update agent params
    await handleRefetchSelectedGroupAgentParams(
      selectedFeatureType,
      selectedCid,
      selectedPartNo,
      selectedPackageNo,
      goldenProductId,
      selectedScope,
    );

    aoiAlert(t('notification.success.applyGroupLinkAction'), ALERT_TYPES.COMMON_INFO);
    return;
  };

  useEffect(() => {
    const component = _.find(allComponents, c => {
      if (_.isInteger(selectedCid) && selectedScope === 'component') return c.region_group_id === selectedCid;
      if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') return c.part_no === selectedPartNo && c.can_group_by_part_no === true;
      if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') return c.package_no === selectedPackageNo && c.can_group_by_package_no === true;
    });

    // console.log('selected group (first matched component info)', component);

    if (!component) return;

    // setSelectedComponent(component);
    setUserDefinedDesignator(_.get(component, 'designator', ''));
    setUserDefinedPackageNo(_.get(component, 'package_no', ''));
    setUserDefinedPartNo(_.get(component, 'part_no', ''));
    setIsComponentPartNoLinked(component.can_group_by_part_no);
    setIsPartNoPackageNoLinked(component.can_group_by_package_no);
    setIsPartNoUnknown(_.isEmpty(component.part_no));
    setIsPackageNoUnknown(_.isEmpty(component.package_no));
  }, [
    allComponents,
    selectedScope,
    selectedCid,
    selectedPackageNo,
    selectedPartNo,
  ]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Collapse: {
            headerBg: '#00000033',
            // headerPadding: '4px 0 4px 8px',
            // contentPadding: '0 0 0 8px',
          }
        }
      }}
    >
      <Collapse
        style={{ width: '100%' }}
        defaultActiveKey={'componentInfo'}
        items={[
          {
            key: 'componentInfo',
            label: <span className='font-source text-[14px] font-normal leading-[normal]'>
              {selectedScope === 'component' && t('productDefine.componentInfo')}
              {selectedScope === 'part' && t('productDefine.partInfo')}
              {selectedScope === 'package' && t('productDefine.packageInfo')}
            </span>,
            children: <div className='flex py-2 px-4 flex-col gap-4 self-stretch'>
              <div className='flex flex-col gap-2 self-stretch'>
                {selectedScope === 'component' &&
                  <div className='flex items-center gap-1 self-stretch flex-1'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.refDesignator')}:
                    </span>
                    <div className='flex items-center self-stretch flex-1'>
                      <Input
                        size='small'
                        style={{ width: '100%' }}
                        disabled={!isEditingInfo}
                        value={userDefinedDesignator}
                        onChange={(e) => {
                          if (strContainsChineseChar(e.target.value)) {
                            aoiAlert(t('notification.error.chineseCharIsNotSupportedInpackageNoPartNoDesignator'), ALERT_TYPES.COMMON_ERROR);
                            setUserDefinedDesignator(removeChineseChar(e.target.value));
                            return;
                          }
                          setUserDefinedDesignator(e.target.value)
                        }}
                      />
                    </div>
                  </div>
                }
                {_.includes(['part', 'component'], selectedScope) &&
                  <div className='flex items-center'>
                    <div className='flex items-center gap-1 self-stretch flex-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.partNo')}:
                      </span>
                      <div className='flex items-center self-stretch flex-1'>
                        <Input
                          size='small'
                          style={{ width: '100%' }}
                          disabled={!isEditingInfo}
                          value={userDefinedPartNo}
                          onChange={(e) => {
                            if (strContainsChineseChar(e.target.value)) {
                              aoiAlert(t('notification.error.chineseCharIsNotSupportedInpackageNoPartNoDesignator'), ALERT_TYPES.COMMON_ERROR);
                              setUserDefinedPartNo(removeChineseChar(e.target.value));
                              return;
                            }
                            setUserDefinedPartNo(e.target.value)
                          }}
                        />
                      </div>
                    </div>
                  </div>
                }
                <div className='flex items-center'>
                  <div className='flex items-center gap-1 self-stretch flex-1'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.packageNo')}:
                    </span>
                    <div className='flex items-center self-stretch flex-1'>
                      <Input
                        size='small'
                        style={{ width: '100%' }}
                        disabled={!isEditingInfo}
                        value={userDefinedPackageNo}
                        onChange={(e) => {
                          if (strContainsChineseChar(e.target.value)) {
                            aoiAlert(t('notification.error.chineseCharIsNotSupportedInpackageNoPartNoDesignator'), ALERT_TYPES.COMMON_ERROR);
                            setUserDefinedPackageNo(removeChineseChar(e.target.value));
                            return;
                          }
                          setUserDefinedPackageNo(e.target.value)
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div
                  className='flex items-center gap-2 self-stretch'
                  style={{
                    pointerEvents: selectedScope !== 'part' && !isPartNoUnknown ? 'none' : 'auto',
                    opacity: selectedScope !== 'part' && !isPartNoUnknown ? 0.5 : 1,
                  }}
                >
                  <Switch
                    disabled={selectedScope !== 'part' && !isPartNoUnknown}
                    size='small'
                    checked={isPartNoPackageNoLinked}
                    onChange={() => {
                      handleLinkingAction(
                        !isPartNoPackageNoLinked ? 'link' : 'unlink',
                        selectedCid,
                        selectedPartNo,
                        selectedPackageNo,
                        selectedScope,
                        goldenProductId,
                        allComponents,
                        selectedFeatureType,
                      );
                    }}
                  />
                  <span className='font-source text-xs font-normal leading-[150%] pt-[3px]'>
                    {t('productDefine.linkPackage')}
                  </span>
                </div>
                <div
                  className='flex items-center gap-2 self-stretch'
                  style={{
                    pointerEvents: selectedScope !== 'component' || isPartNoUnknown ? 'none' : 'auto',
                    opacity: selectedScope !== 'component' || isPartNoUnknown ? 0.5 : 1,
                  }}
                >
                  <Switch
                    disabled={selectedScope !== 'component' || isPartNoUnknown}
                    size='small'
                    checked={isComponentPartNoLinked}
                    onChange={() => {
                      handleLinkingAction(
                        !isComponentPartNoLinked ? 'link' : 'unlink',
                        selectedCid,
                        selectedPartNo,
                        selectedPackageNo,
                        selectedScope,
                        goldenProductId,
                        allComponents,
                        selectedFeatureType,
                      );
                    }}
                  />
                  <span className='font-source text-xs font-normal leading-[150%] pt-[3px]'>
                    {t('productDefine.linkPart')}
                  </span>
                </div>
              </div>
              {!isEditingInfo ?
                <div className='flex py-4 gap-4 self-stretch flex-col'>
                  <div className='flex gap-4 self-stretch'>
                    {selectedScope === 'component' &&
                      <Button
                        style={{ width: '50%' }}
                        onClick={() => setIsDrawModeEnabled(true)}
                      >
                        <div className='flex items-center gap-2'>
                          <div className='flex w-4 h-4 justify-center items-center'>
                            <img src='/icn/squareDot_white.svg' alt='locator' className='w-3 h-3' />
                          </div>
                          <span className='font-source text-[12px] font-normal text-white leading-[150%] pt-0.5'>
                              {t('common.drawRoi')}
                            </span>
                        </div>
                      </Button>
                    }
                    {selectedScope === 'component' &&
                      <Button
                        style={{ width: selectedScope === 'component' ? '50%' : '100%' }}
                        onClick={() => {
                          setIsEditingInfo(true);
                        }}
                      >
                        <div className='flex items-center gap-2'>
                          <div className='flex w-4 h-4 justify-center items-center'>
                            <img src='/icn/squareDot_white.svg' alt='locator' className='w-3 h-3' />
                          </div>
                          <span className='font-source text-[12px] font-normal text-white leading-[150%] pt-0.5'>
                            {t('common.editInfo')}
                          </span>
                        </div>
                      </Button>
                    }
                  </div>
                  { selectedScope === 'component' &&
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        setIsAddFromLibraryOpened(true);
                      }}
                    >
                      <div className='flex items-center gap-2'>
                        <div className='flex w-4 h-4 justify-center items-center'>
                          <img src='/icn/pencil_white.svg' alt='locator' className='w-3 h-3' />
                        </div>
                        <span className='font-source text-[12px] font-normal text-white leading-[150%] pt-0.5'>
                          {t('productDefine.replaceFeaturesWithTemplateFromLibrary')}
                        </span>
                      </div>
                    </Button>
                  }
                </div>
              :
                <div className='flex items-center gap-4 self-stretch py-4'>
                  <Button
                    style={{ width: '50%' }}
                    onClick={() => {
                      setIsEditingInfo(false);
                    }}
                  >
                    <span className='font-source text-[12px] font-normal text-white leading-[150%] pt-0.5'>
                      {t('common.cancel')}
                    </span>
                  </Button>
                  <Button
                    style={{ width: '50%' }}
                    onClick={() => {
                      handleSaveComponentInfo(
                        userDefinedDesignator,
                        userDefinedPackageNo,
                        userDefinedPartNo,
                        selectedScope,
                        selectedCid,
                        allComponents,
                        allFeatures,
                        goldenProductId,
                      );
                      setIsEditingInfo(false);
                    }}
                  >
                    <span className='font-source text-[12px] font-normal text-white leading-[150%] pt-0.5'>
                      {t('common.save')}
                    </span>
                  </Button>
                </div>
              }
            </div>
          }
        ]}
      >
      </Collapse>
    </ConfigProvider>
  );
};

export default ComponentInfo;