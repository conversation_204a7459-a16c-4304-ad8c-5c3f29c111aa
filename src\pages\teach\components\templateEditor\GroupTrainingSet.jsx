import { Button, Select, Switch, Tabs, Tooltip } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _, { set } from 'lodash';
import { useAnnotateGroupMutation, useCancelAnnotateGroupMutation, useCancelFeedbackMutation, useGetDataSetExampleQuery, useGetInspectedComponentQuery, useLazyGetDataSetExampleQuery, useLazyGetInferenceStatusQuery, useLazyGetInspectedComponentQuery, useMarkTrainingExampleMutation, useModelUpdateTriggerMutation } from '../../../../services/inference';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { componentRoiPadding, filletLowerThreshold, filletOpenThreshold, filletUpperThreshold, filletVolumeRatio, highResoluCroppedDisplayMegaPixelCount, serverHost, templateEditorLocateRectWaitTime, trainingSetCropImgPadding, mountingInspection3D, leadInspection3D, solderInspection3D, solderInspection2D, isAOI2DSMT, leadFeatureType, leadGapFeatureType, leadInspection2D, isAOI3DSMT, solder2DValidRatioRanges, validRatioList, solderValidRatioList, padValidRatioList, tipValidRatioList, solderValidRatioRange, liftedLeadTipValidRatioRange, leadInspection2DBase, variantComponentErrorType, textVerification, modelTypes, agentParamStatsList } from '../../../../common/const';
import { SmallTabsConfigProvider } from '../../../../common/styledComponent';
import { systemApi } from '../../../../services/system';
import { useSelector } from 'react-redux';
import { getAgentParamTypeNValueByParam, getRunningTaskGoldenProductIds, sleep, twoDLeadGapAgentParamCheck } from '../../../../common/util';
import InferenceResult from '../../../../components/InferenceResult';
import FilterOutlined from '@ant-design/icons/FilterOutlined';
import LeadSegmentRecordCard from './LeadSegmentRecordCard';
import LeadGapSegmentRecordCard from './LeadGapSegmentRecordCard';
import WindowedMaskDisplay from './WindowedMaskDisplay';
import { setContainerLvlLoadingMsg, setCurTrainingTaskStartTime, setIsContainerLvlLoadingEnabled, setIsTrainingRunning } from '../../../../reducer/setting';


const GroupTrainingSet = (props) => {
  const {
    reevaluateTriggered,
    setReevaluateTriggered,
    trainingSetSelectedDetail,
    setTrainingSetSelectedDetail,
    trainingSetSelectedErrorType,
    setTrainingSetSelectedErrorType,
    selectedFeatureType,
    setSelectedFid,
    selectedCid,
    componentListGroupMode,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    selectedScope,
    allComponents,
    selectedGroupFeatureTypeAgentParams,
    setSelectedCid,
    allFeatures,
    setRequiredLocateRect,
    clickFromTrainingSetCardRef,
    selectedArrayIndex,
    setSelectedArrayIndex,
    mmToPixelRatio,
    isPesudoColorDisplayed,
    setIsPesudoColorDisplayed,
    selectedFid,
    shouldTrainingSetRefetch,
    setShouldTrainingSetRefetch,
    setSelectedFeatureType,
    handleRefetchSelectedGroupAgentParams,
    setShouldDatasetInComponentListRefetch,
  } = props;

  const tableContainerRef = useRef(null);

  const { t } = useTranslation();

  const [displayedItems, setDisplayedItems] = useState([]);
  const [passedOnly, setPassedOnly] = useState(null);
  const [hasFeedbackOnly, setHasFeedbackOnly] = useState(null);
  const [tableHeight, setTableHeight] = useState(0);
  const [selectedLineItemResultKey, setSelectedLineItemResultKey] = useState(null);
  const [selectedDetail, setSelectedDetail] = useState(null);
  const [trainingExample, setTrainingExample] = useState(null);
  const [selectedLineItemResult, setSelectedLineItemResult] = useState(null);
  const [selectedLineItemResultParsedError, setSelectedLineItemResultParsedError] = useState(null);
  const [selectedErrorCategory, setSelectedErrorCategory] = useState(null);
  const [feedbackErrorType, setFeedbackErrorType] = useState(null);
  const [isResizing, setIsResizing] = useState(false);
  const [minTableHeight] = useState(200);
  const [maxTableHeight] = useState(600);
  const [maskViewerList, setMaskViewerList] = useState([]);
  // const [isPesudoColorDisplayed, setIsPesudoColorDisplayed] = useState(false);
  const [displayedCardCount, setDisplayedCardCount] = useState(0);

  const [lazyGetDataExample] = useLazyGetDataSetExampleQuery();
  const [markTrainingExample] = useMarkTrainingExampleMutation();
  const [lazyGetInspectedComponent] = useLazyGetInspectedComponentQuery();

  const handleMouseDown = (e) => {
    setIsResizing(true);
    e.preventDefault();
  };

  const handleMouseMove = React.useCallback((e) => {
    if (!isResizing || !tableContainerRef.current) return;

    const containerRect = tableContainerRef.current.getBoundingClientRect();
    const newHeight = e.clientY - containerRect.top;

    if (newHeight >= minTableHeight && newHeight <= maxTableHeight) {
      setTableHeight(newHeight);
    }
  }, [isResizing, minTableHeight, maxTableHeight]);

  const handleMouseUp = React.useCallback(() => {
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleRefetch = async (
    goldenProductId,
    passedOnly,
    selectedFeatureType,
    selectedCid,
    selectedScope,
    selectedPartNo,
    selectedPackageNo,
    allFeatures,
    selectedFid,
    selectedArrayIndex,
    shouldUpdateLineItemDetail=true,
    allComponents,
  ) => {
    const query = generateCurrentQuery(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      selectedScope,
      selectedPartNo,
      selectedPackageNo,
    );

    await updateDisplayedItems(query, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType, shouldUpdateLineItemDetail,
      allComponents,
      selectedScope,
    );
  };

  const generateCurrentQuery = (
    goldenProductId,
    passedOnly,
    selectedFeatureType,
    selectedCid,
    selectedScope,
    selectedPartNo,
    selectedPackageNo,
  ) => {
    let payload = {
      golden_product_id: Number(goldenProductId),
      step: 0,
      has_feedback: true,
      feature_type: selectedFeatureType === leadGapFeatureType ? leadFeatureType : selectedFeatureType,
    };

    if (selectedScope === 'component' && _.isInteger(selectedCid)) payload.component_id = selectedCid;
    if (selectedScope === 'part' && !_.isEmpty(selectedPartNo)) payload.part_no = selectedPartNo;
    if (selectedScope === 'package' && !_.isEmpty(selectedPackageNo)) payload.package_no = selectedPackageNo;

    if (_.isBoolean(passedOnly)) payload.reevaluation_pass = passedOnly;
    // if (_.isBoolean(hasFeedbackOnly)) payload.annotated = hasFeedbackOnly;

    return payload;
  };

  const updateDisplayedItems = async (query, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType,
    shouldUpdateLineItemDetail=true,
    allComponents,
    selectedScope,
  ) => {
    const res = await lazyGetDataExample(query);

    if (res.error) {
      aoiAlert(t('notification.error.getDataExample'), ALERT_TYPES.COMMON_ERROR);
      console.error('getDataExample error:', _.get(res, 'error.message', ''));
      return;
    }

    // flatten the data
    const result = {};

    // 2025/08/15
    // map all all features and components by cid and fid
    // since lazyGetDataExample's response will not filter unlinked result
    // we need to filter them out by checking component dto's can_group_by_package_no and can_group_by_part_no...
    const cidToComponentMap = _.keyBy(allComponents, c => _.get(c, 'region_group_id', 0));
    const fidToFeatureMap = _.keyBy(allFeatures, f => _.get(f, 'feature_id', 0));

    for (const agentResult of res.data.line_item_results) {
      // if (
      //   (agentResult.feature_type !== query.feature_type && !_.startsWith(query.feature_type, '_text')) ||
      //   (_.startsWith(query.feature_type, '_text') && !agentResult.feature_type.startsWith('_text'))
      // ) continue;

      // each result is not providing the definition component id(their component id is different from definition component id)
      // but their feature is the same as definition feature id
      if (selectedScope !== 'component') {
        const dcid = _.get(fidToFeatureMap, `${agentResult.feature_id}.group_id`, null);
        if (!_.isInteger(dcid)) continue;
        const componentObj = _.get(cidToComponentMap, dcid, null);
        if (!componentObj) continue;

        if (selectedScope === 'part' && !componentObj.can_group_by_part_no) continue;
        if (selectedScope === 'package' && !componentObj.can_group_by_package_no) continue;
      }

      if (agentResult.feature_type !== query.feature_type) continue;
      if (_.has(result, `${agentResult.component_id}_${agentResult.feature_id}_${agentResult.array_index}`)) {
        result[`${agentResult.component_id}_${agentResult.feature_id}_${agentResult.array_index}`].push(agentResult);
      } else {
        result[`${agentResult.component_id}_${agentResult.feature_id}_${agentResult.array_index}`] = [agentResult];
      }

      // if (agentResult.training_example) trainingExample = agentResult;
    }

    if (_.isEmpty(result)) {
      setDisplayedItems({});
      setDisplayedCardCount(0);
      return;
    }

    setDisplayedItems(result);
    
    // count the number of cards for lead case we need to count the number of leads and gaps
    if (selectedFeatureType === leadFeatureType) {
      // const leadF = _.filter(res.data.line_item_results, r => r.detail === leadInspection2D);
      // const leadFCount = leadF.length;
      // const sampleFid = _.get(leadF, '0.feature_id', null);
      // if (!_.isInteger(sampleFid)) return;
      // const sampleFeatureLeadCount = _.get(_.find(allFeatures, f => f.feature_id === sampleFid), `line_item_params.${leadInspection2D}.params.lead_count.param_int.value`, 0);
      // setDisplayedCardCount(leadFCount * sampleFeatureLeadCount, 0);
      const leadFid = _.uniq(_.map(_.filter(res.data.line_item_results, r => r.detail === leadInspection2DBase), r => r.feature_id));
      let count = 0;
      for (const fid of leadFid) {
        const features = _.filter(allFeatures, f => f.feature_id === fid);
        for (const f of features) {
          const leadCount = _.get(f, `line_item_params.${leadInspection2DBase}.params.lead_count.param_int.value`, 0);
          count += leadCount;
        }
      }
      setDisplayedCardCount(count);
    } else if (selectedFeatureType === leadGapFeatureType) {
      // const leadF = _.filter(res.data.line_item_results, r => r.detail === leadInspection2D);
      // const leadFCount = leadF.length;
      // const sampleFid = _.get(leadF, '0.feature_id', null);
      // if (!_.isInteger(sampleFid)) return;
      // const sampleFeatureLeadCount = _.get(_.find(allFeatures, f => f.feature_id === sampleFid), `line_item_params.${leadInspection2D}.params.lead_count.param_int.value`, 0);
      // setDisplayedCardCount(leadFCount * (sampleFeatureLeadCount - 1), 0);
      const leadFid = _.uniq(_.map(_.filter(res.data.line_item_results, r => r.detail === leadInspection2DBase), r => r.feature_id));
      let count = 0;
      for (const fid of leadFid) {
        const features = _.filter(allFeatures, f => f.feature_id === fid);
        for (const f of features) {
          const leadCount = _.get(f, `line_item_params.${leadInspection2DBase}.params.lead_count.param_int.value`, 0);
          count += Math.max(leadCount - 1, 0);
        }
      }
      setDisplayedCardCount(count);
    } else {
      // setDisplayedCardCount(_.reduce(_.keys(result), (acc, key) => acc + result[key].length, 0));
      setDisplayedCardCount(
        _.keys(result).length
      )
    }

    // setSelectedDetail(_.get(result, `[${_.keys(result)[0]}][0].detail`, null));
    // setSelectedLineItemResult(result[_.keys(result)[0]]);

    if (shouldUpdateLineItemDetail && !clickFromTrainingSetCardRef.current) {
      if (
        _.isInteger(selectedCid) &&
        _.isInteger(selectedFid)
      ) {
        setSelectedLineItemResultKey(`${selectedCid}_${selectedFid}_${selectedArrayIndex}`);
        setSelectedDetail(_.get(result, `[${selectedCid}_${selectedFid}_${selectedArrayIndex}][0].detail`, null));
        setSelectedLineItemResult(result[`${selectedCid}_${selectedFid}_${selectedArrayIndex}`]);
      } else {
        setSelectedLineItemResultKey(_.keys(result)[0]);
        setSelectedDetail(_.get(result, `[${_.keys(result)[0]}][0].detail`, null));
        setSelectedLineItemResult(result[_.keys(result)[0]]);
      }
    }

    if (clickFromTrainingSetCardRef.current) clickFromTrainingSetCardRef.current = false;
  };

  useEffect(() => {
    if (!shouldTrainingSetRefetch) return;

    const query = generateCurrentQuery(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      selectedScope,
      selectedPartNo,
      selectedPackageNo,
    );

    updateDisplayedItems(
      query,
      allFeatures,
      selectedFid,
      selectedCid,
      selectedArrayIndex,
      selectedFeatureType,
      true,
      allComponents,
      selectedScope,
    );

    setShouldTrainingSetRefetch(false);
  }, [shouldTrainingSetRefetch]);


  useEffect(() => {
    const query = generateCurrentQuery(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      selectedScope,
      selectedPartNo,
      selectedPackageNo,
    );

    updateDisplayedItems(
      query,
      allFeatures,
      selectedFid,
      selectedCid,
      selectedArrayIndex,
      selectedFeatureType,
      true,
      allComponents,
      selectedScope,
    );
  }, [
    passedOnly,
    hasFeedbackOnly,
    selectedFeatureType,
    // selectedCid,
    selectedScope,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    selectedArrayIndex,
  ]);

  useEffect(() => {
    if (!reevaluateTriggered) return;

    const refetch = async (
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      selectedScope,
      selectedPartNo,
      selectedPackageNo,
      allFeatures,
      selectedFid,
      selectedArrayIndex,
      allComponents,
    ) => {
      const query = generateCurrentQuery(
        goldenProductId,
        passedOnly,
        selectedFeatureType,
        selectedCid,
        selectedScope,
        selectedPartNo,
        selectedPackageNo,
      );

      await updateDisplayedItems(query, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType,
        true,
        allComponents,
        selectedScope,
      );

      setReevaluateTriggered(false);
    };

    refetch(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      selectedScope,
      selectedPartNo,
      selectedPackageNo,
      allFeatures,
      selectedFid,
      selectedArrayIndex,
      allComponents,
    );
  }, [reevaluateTriggered]);

  useEffect(() => {
    if (_.isEmpty(selectedLineItemResult)) return;

    const ipcProductId = _.get(selectedLineItemResult, '[0].product_id', 0);
    const rcid = _.get(selectedLineItemResult, '[0].component_id', -1);

    const run = async (ipcProductId, rcid) => {
      const res = await lazyGetInspectedComponent({
        inspected_product_id: ipcProductId,
        step: 0,
      }, false);

      if (res.error) {
        aoiAlert(t('notification.error.getInspectedComponent'), ALERT_TYPES.COMMON_ERROR);
        console.error('getInspectedComponent error:', _.get(res, 'error.message', ''));
        return;
      }

      const componentInferenceResult = _.find(res.data, i => i.result_component_id === rcid);
      setFeedbackErrorType(_.get(componentInferenceResult, 'feedback_error_type', null));
    };

    run(ipcProductId, rcid);
  }, [
    selectedLineItemResult,
    selectedFeatureType,
  ]);

  useEffect(() => {
    if (tableContainerRef.current && tableHeight === 0) {
      setTableHeight(300);
    }
  }, [tableHeight]);

  const handleMaskChange = (info, lineItemKey, agentResult) => {
    if (info.show) {
      const componentInfo = {
        color_map_uri: _.get(agentResult, 'component_color_map_uri', ''),
        depth_map_uri: _.get(agentResult, 'component_depth_map_uri', ''),
        shape: {
          points: [
            { x: 0, y: 0 },
            {
              x: _.get(agentResult, 'roi.points[1].x', 0) - _.get(agentResult, 'roi.points[0].x', 0),
              y: _.get(agentResult, 'roi.points[1].y', 0) - _.get(agentResult, 'roi.points[0].y', 0),
            },
          ],
          angle: _.get(agentResult, 'roi.angle', 0),
        },
      };
      const featureInfo = { roi: _.get(agentResult, 'roi', {}) };
      const id = _.uniqueId('mask_');
      setMaskViewerList(prev => [...prev, { id, key: lineItemKey, componentInfo, featureInfo, maskImage: info.masks }]);
    } else {
      setMaskViewerList(prev => prev.filter(m => m.key !== lineItemKey));
    }
  };

  return (
    <>
      {maskViewerList.map((m) => (
        <WindowedMaskDisplay
          key={m.id}
          id={m.id}
          componentInfo={m.componentInfo}
          featureInfo={m.featureInfo}
          maskImage={m.maskImage}
          selfUnmount={(id) => setMaskViewerList(prev => prev.filter(w => w.id !== id))}
        />
      ))}
      <div className='flex flex-col flex-1 self-stretch'>
      <div className='flex flex-col items-start gap-2 self-stretch bg-[#ffffff0d]'>
        <div className='flex p-4 flex-col justify-center items-start gap-2 self-stretch'>
          <div className='flex items-center justify-between self-stretch'>
            <div className='flex items-center gap-1 min-w-0 flex-1 mr-4'>
              {selectedScope === 'package' &&
                <Fragment>
                  <span
                    className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] truncate'
                    title={selectedPackageNo}
                  >
                    {selectedPackageNo}
                  </span>
                  <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                    /
                  </span>
                </Fragment>
              }
              {selectedScope === 'part' &&
                <Fragment>
                  <span
                    className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] truncate'
                    title={selectedPartNo}
                  >
                    {selectedPartNo}
                  </span>
                  <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                    /
                  </span>
                </Fragment>
              }
              {selectedScope === 'component' &&
                <Fragment>
                  <span
                    className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] truncate'
                    title={_.get(_.find(allComponents, c => c.region_group_id === selectedCid), 'designator', '')}
                  >
                    {_.get(_.find(allComponents, c => c.region_group_id === selectedCid), 'designator', '')}
                  </span>
                  <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                    /
                  </span>
                </Fragment>
              }
              <span
                className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] truncate'
                title={t(`leadFeatureTypeText.${selectedFeatureType.startsWith('_text') ? '_text': selectedFeatureType}`)}
              >
                {t(`leadFeatureTypeText.${selectedFeatureType.startsWith('_text') ? '_text': selectedFeatureType}`)}
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                /
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                {t('productDefine.trainingSet')}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                {t('productDefine.allItems')}:
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                {/* {displayedItems.length} */}
                {/* {_.reduce(_.keys(displayedItems), (acc, key) => acc + displayedItems[key].length, 0)} */}
                {displayedCardCount}
              </span>
            </div>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <FilterOutlined />
            <Select
              popupMatchSelectWidth={false}
              style={{ width: '80%' }}
              size='small'
              value={passedOnly}
              onChange={(value) => {
                setPassedOnly(value);
              }}
              options={[
                {
                  value: true,
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.passedOnly')}
                  </span>
                },
                {
                  value: false,
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.failedOnly')}
                  </span>
                },
                {
                  value: null,
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.showAll')}
                  </span>
                }
              ]}
            />
            {isAOI3DSMT &&
              <Tooltip
                title={
                  <div className='flex p-2 flex-col gap-2 self-stretch'>
                    <div className='flex items-center gap-2'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.displayPesdoColor')}
                      </span>
                      <Switch
                        checked={isPesudoColorDisplayed}
                        onChange={() => setIsPesudoColorDisplayed(!isPesudoColorDisplayed)}
                        size='small'
                      />
                    </div>
                  </div>
                }
              >
                <div className='flex items-center gap-2 justiify-center'>
                  <img
                    src='/icn/gear_white.svg'
                    className='w-4 h-4 cursor-pointer'
                  />
                </div>
              </Tooltip>
            }
          </div>
        </div>
      </div>
      <div className='flex flex-col self-stretch'>
        <div
          ref={tableContainerRef}
          className='flex self-stretch'
          style={{ height: `${tableHeight}px` }}
        >
          <div
            className='flex items-center flex-wrap gap-2 self-stretch overflow-auto px-2'
            style={{
              height: tableHeight,
            }}
          >
            {tableHeight > 0 && _.map(_.keys(displayedItems), (key) => {
            const item = displayedItems[key];
            if (selectedFeatureType === leadFeatureType) {
              const feature = _.find(allFeatures, f => f.feature_id === _.get(item, '[0].feature_id', null));
              const agentName = isAOI2DSMT ? leadInspection2D : leadInspection3D;
              const leadCount = _.get(feature, `line_item_params.${agentName}.params.lead_count.param_int.value`, 0);
              return _.map(_.range(leadCount), (i) => (
                <LeadSegmentRecordCard
                  isPesudoColorDisplayed={isPesudoColorDisplayed}
                  key={`${key}_${i}`}
                  selected={key === selectedLineItemResultKey}
                  selectedLineItemResult={item}
                  leadIndex={i}
                  setSelectedLineItemResultKey={setSelectedLineItemResultKey}
                  setSelectedDetail={setTrainingSetSelectedDetail}
                  trainingExample={trainingExample}
                  setSelectedLineItemResult={setSelectedLineItemResult}
                  setSelectedLineItemResultParsedError={setSelectedLineItemResultParsedError}
                  setSelectedFid={setSelectedFid}
                  setSelectedCid={setSelectedCid}
                  allFeatures={allFeatures}
                  setRequiredLocateRect={setRequiredLocateRect}
                  clickFromTrainingSetCardRef={clickFromTrainingSetCardRef}
                  setSelectedArrayIndex={setSelectedArrayIndex}
                  mmToPixelRatio={mmToPixelRatio}
                  goldenProductId={goldenProductId}
                />
              ));
            }
            if (selectedFeatureType === leadGapFeatureType) {
              const feature = _.find(allFeatures, f => f.feature_id === _.get(item, '[0].feature_id', null));
              const agentName = isAOI2DSMT ? leadInspection2D : leadInspection3D;
              const leadCount = _.get(feature, `line_item_params.${agentName}.params.lead_count.param_int.value`, 0);
              return _.map(_.range(Math.max(leadCount - 1, 0)), (i) => (
                <LeadGapSegmentRecordCard
                  isPesudoColorDisplayed={isPesudoColorDisplayed}
                  key={`${key}_gap_${i}`}
                  selected={key === selectedLineItemResultKey}
                  selectedLineItemResult={item}
                  gapIndex={i}
                  setSelectedLineItemResultKey={setSelectedLineItemResultKey}
                  setSelectedDetail={setTrainingSetSelectedDetail}
                  trainingExample={trainingExample}
                  setSelectedLineItemResult={setSelectedLineItemResult}
                  setSelectedLineItemResultParsedError={setSelectedLineItemResultParsedError}
                  setSelectedFid={setSelectedFid}
                  setSelectedCid={setSelectedCid}
                  allFeatures={allFeatures}
                  setRequiredLocateRect={setRequiredLocateRect}
                  clickFromTrainingSetCardRef={clickFromTrainingSetCardRef}
                  setSelectedArrayIndex={setSelectedArrayIndex}
                  mmToPixelRatio={mmToPixelRatio}
                  goldenProductId={goldenProductId}
                  selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                />
              ));
            }
            return (
              <LineItemRecordCard
                isPesudoColorDisplayed={isPesudoColorDisplayed}
                // key={`${item.product_id}_${item.inspection_result_id}`}
                key={key}
                selected={key === selectedLineItemResultKey}
                selectedLineItemResult={item}
                setSelectedLineItemResultKey={setSelectedLineItemResultKey}
                setSelectedDetail={setTrainingSetSelectedDetail}
                trainingExample={trainingExample}
                setSelectedLineItemResult={setSelectedLineItemResult}
                setSelectedLineItemResultParsedError={setSelectedLineItemResultParsedError}
                setSelectedFid={setSelectedFid}
                setSelectedCid={setSelectedCid}
                allFeatures={allFeatures}
                setRequiredLocateRect={setRequiredLocateRect}
                clickFromTrainingSetCardRef={clickFromTrainingSetCardRef}
                setSelectedArrayIndex={setSelectedArrayIndex}
                setSelectedFeatureType={setSelectedFeatureType}
                handleRefetchSelectedGroupAgentParams={handleRefetchSelectedGroupAgentParams}
                goldenProductId={goldenProductId}
              />
            );
          })}
        </div>
      </div>
      <div
        className={`flex h-[4px] self-stretch cursor-row-resize bg-gray-600 hover:bg-blue-500 transition-colors duration-200 ${
          isResizing ? 'bg-blue-500' : ''
        }`}
        onMouseDown={handleMouseDown}
        title="拖拽调整表格高度"
      >
        <div className='flex w-full h-full items-center justify-center'>
          <div className='w-8 h-0.5 bg-white rounded opacity-60'></div>
        </div>
      </div>
      </div>
      <div className='flex flex-1 py-2 flex-col self-stretch border-t-[1px] border-t-gray-1 bg-[#ffffff08]'>
        <div className='flex py-1 px-2 flex-col gap-2 self-stretch flex-1'>
          <div className='flex px-2 gap-1 self-stretch items-center'>
            {!_.isNull(selectedLineItemResultKey) && !_.isNull(trainingSetSelectedDetail) && (
              <Button
                onClick={() => {
                  const run = async (selectedLineItemResult, refetchPayload, displayedItems, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType,
                    allComponents,
                    selectedScope,
                  ) => {
                    for (const agentResult of selectedLineItemResult) {
                      let inspection_result_id = _.get(agentResult, 'inspection_result_id', null);
                      if (_.isNull(inspection_result_id)) {
                        const curTrainingExample = _.find(displayedItems, i => {
                          let f = true;
                          for (const a of _.get(i, 'agentResults', [])) {
                            if (!a.training_example) f = false;
                          }
                          return f;
                        });
                        for (const a of _.get(curTrainingExample, 'agentResults', [])) {
                          const res = await markTrainingExample({
                            inspection_result_id: _.get(a, 'inspection_result_id', null),
                            mark: false,
                          });
                          if (res.error) {
                            aoiAlert(t('notification.error.markTrainingExample'), ALERT_TYPES.COMMON_ERROR);
                            console.error('markTrainingExample error:', _.get(res, 'error.message', ''));
                            return;
                          }
                        }

                        await updateDisplayedItems(refetchPayload, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType,
                          true,
                          allComponents,
                          selectedScope,
                        );
                        return;
                      }
                      const res = await markTrainingExample({
                        inspection_result_id,
                        mark: !_.isNull(_.get(selectedLineItemResult, 'agentResults[0].inspection_result_id', null)),
                      });

                      if (res.error) {
                        aoiAlert(t('notification.error.markTrainingExample'), ALERT_TYPES.COMMON_ERROR);
                        console.error('markTrainingExample error:', _.get(res, 'error.message', ''));
                        return;
                      }
                    }
                    await updateDisplayedItems(refetchPayload, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType,
                      true,
                      allComponents,
                      selectedScope,
                    );
                  };

                  const selectedLineItemResult = _.find(displayedItems, (item) => item.line_item_result_key === selectedLineItemResultKey);

                  const refetchPayload = generateCurrentQuery(
                    goldenProductId,
                    passedOnly,
                    selectedFeatureType,
                    selectedCid,
                    selectedScope,
                    selectedPartNo,
                    selectedPackageNo,
                  );

                  run(selectedLineItemResult, refetchPayload, displayedItems, allFeatures, selectedFid, selectedCid, selectedArrayIndex, selectedFeatureType,
                    allComponents,
                    selectedScope,
                  );
                }}
              >
                <div className='flex items-center gap-1'>
                  <div className='flex items-center justify-center w-[16px] h-[16px]'>
                    <img
                      src='/icn/star_yellow.svg'
                      alt='star'
                      className='w-[10px] h-[10px]'
                    />
                  </div>
                  <span className='font-source text-[12px] font-normal leading-[normal]'>
                    {t('productDefine.setAsGolden')}
                  </span>
                </div>
              </Button>
            )}
          </div>
          {/* {selectedFeatureType !== leadGapFeatureType && ( */}
            <SelectedFeatureErrorInfo
              selectedLineItemResult={selectedLineItemResult}
              selectedDetail={trainingSetSelectedDetail}
              setSelectedDetail={setTrainingSetSelectedDetail}
              trainingSetSelectedErrorType={trainingSetSelectedErrorType}
              setTrainingSetSelectedErrorType={setTrainingSetSelectedErrorType}
              handleRefetch={handleRefetch}
              feedbackErrorType={feedbackErrorType}
              selectedFeatureType={selectedFeatureType}
              selectedPartNo={selectedPartNo}
              selectedPackageNo={selectedPackageNo}
              goldenProductId={goldenProductId}
              passedOnly={passedOnly}
              componentListGroupMode={componentListGroupMode}
              selectedCid={selectedCid}
              selectedScope={selectedScope}
              selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
              allFeatures={allFeatures}
              selectedArrayIndex={selectedArrayIndex}
              selectedFid={selectedFid}
              selectedLineItemResultKey={selectedLineItemResultKey}
              onMaskChange={handleMaskChange}
              setFeedbackErrorType={setFeedbackErrorType}
              allComponents={allComponents}
              setShouldTrainingSetRefetch={setShouldTrainingSetRefetch}
              setShouldDatasetInComponentListRefetch={setShouldDatasetInComponentListRefetch}
            />
          {/* )} */}
        </div>
      </div>
    </div>
    </>
  );
};

const LineItemRecordCard = (props) => {
  const {
    selected,
    selectedLineItemResult,
    setSelectedLineItemResultKey,
    setSelectedDetail,
    trainingExample,
    setSelectedLineItemResult,
    setSelectedLineItemResultParsedError,
    setSelectedFid,
    setSelectedCid,
    allFeatures,
    setRequiredLocateRect,
    clickFromTrainingSetCardRef,
    setSelectedArrayIndex,
    isPesudoColorDisplayed,
    setSelectedFeatureType,
    handleRefetchSelectedGroupAgentParams,
    goldenProductId,
  } = props;

  const { t } = useTranslation();

  const [url, setUrl] = useState(null);
  const [passStatus, setPassStatus] = useState(null);
  const [latestPassStatus, setLatestPassStatus] = useState('empty');
  const [isGroudTrueGreen, setIsGroudTrueGreen] = useState(true);

  useEffect(() => {
    const firstAgent = _.get(selectedLineItemResult, '[0]', {});

    let tmpUrl = `${serverHost}/blob?type=${isPesudoColorDisplayed ? 'depth_map' : 'image'}`;
    tmpUrl = tmpUrl.concat(`&color_uri=${encodeURIComponent(_.get(firstAgent, 'component_color_map_uri', ''))}`);
    tmpUrl = tmpUrl.concat(`&depth_uri=${encodeURIComponent(_.get(firstAgent, 'component_depth_map_uri', ''))}`);
    tmpUrl = tmpUrl.concat(`&x_min=${_.get(firstAgent, 'roi.points[0].x', 0)}`);
    tmpUrl = tmpUrl.concat(`&y_min=${_.get(firstAgent, 'roi.points[0].y', 0)}`);
    tmpUrl = tmpUrl.concat(`&x_max=${_.get(firstAgent, 'roi.points[1].x', 0)}`);
    tmpUrl = tmpUrl.concat(`&y_max=${_.get(firstAgent, 'roi.points[1].y', 0)}`);
    tmpUrl = tmpUrl.concat(`&angle=${_.get(firstAgent, 'roi.angle', 0)}`);
    tmpUrl = tmpUrl.concat(`&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`);
    tmpUrl = tmpUrl.concat(`&t=${Date.now()}`);

    setUrl(tmpUrl);

    let flag;
    for (const a of selectedLineItemResult) {
      if (_.isEmpty(_.get(a, 'reevaluation_result', {}))) {
        flag = 'empty';
      } else {
        flag = _.get(a, 'reevaluation_result.pass', false);
        if (!flag) break;
      }
    }

    setPassStatus(_.get(selectedLineItemResult, '[0].pass', false));
    setLatestPassStatus(flag);

    if (_.get(selectedLineItemResult, '[0].training_example', false)) {
      setIsGroudTrueGreen(true);
    } else {
      // search for agent result that has feedback
      for (const r of selectedLineItemResult) {
        if (!_.isEmpty(_.get(r, 'feedback', {}))) {
          if (
            (_.get(r, 'pass', false) && _.get(r, 'feedback.correct', false)) ||
            (!_.get(r, 'pass', false) && !_.get(r, 'feedback.correct', false))
          ) {
            setIsGroudTrueGreen(true);
          } else {
            setIsGroudTrueGreen(false);
          }
          break;
        }
      }
    }
  }, [selectedLineItemResult, isPesudoColorDisplayed]);

  return (
    <div
      className={`flex p-1 flex-col gap-0.5 items-center justify-center rounded-[2px] border-[1px] border-gray-2
        ${selected ? 'border-gray-3 bg-[#ffffff1a]' : 'bg-transparent'} cursor-pointer hover:bg-[#ffffff1a] transition-all duration-300`}
      onClick={() => {
        const dcid = _.get(_.find(allFeatures, f => f.feature_id === _.get(selectedLineItemResult, '[0].feature_id', null)), 'group_id', null);
        const cid = _.get(selectedLineItemResult, '[0].component_id', null);
        const fid = _.get(selectedLineItemResult, '[0].feature_id', null);
        const arrayIndex = _.get(selectedLineItemResult, '[0].array_index', null);
        // console.log('selectedLineItemResult', selectedLineItemResult);
        clickFromTrainingSetCardRef.current = true;
        setSelectedCid(dcid);
        setSelectedFid(fid);
        // setSelectedLineItemResultKey(_.get(selectedLineItemResult, 'line_item_result_key'));
        setSelectedLineItemResultKey(`${cid}_${fid}_${arrayIndex}`);
        setSelectedDetail(_.get(selectedLineItemResult, '[0].detail'));
        setSelectedLineItemResult(selectedLineItemResult);
        setSelectedArrayIndex(arrayIndex);
        // if (_.startsWith(_.get(selectedLineItemResult, '[0].feature_type', null), '_text')) {
        //   handleRefetchSelectedGroupAgentParams(
        //     _.get(selectedLineItemResult, '[0].feature_type', null),
        //     dcid,
        //     null,
        //     null,
        //     goldenProductId,
        //     'component',
        //   );
        // }
        // console.log('selectedLineItemResult', selectedLineItemResult);
        // console.log('feature', _.get(selectedLineItemResult, 'agentResults[0].feature_id', null));
        const locate = async (cid, fid) => {
          await sleep(250);
          
          await sleep(templateEditorLocateRectWaitTime);
          setRequiredLocateRect({
            cid,
            fid,
          });
        };
        locate(
          dcid,
          fid,
        );
      }}
    >
      <img
        src={url}
        className='w-[120px] h-[120px] object-contain'
        alt='line item'
      />

      <div className="flex h-2 items-center gap-0.5 self-stretch w-[120px] mx-auto">
        {/* <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${passStatus ? '[background:var(--Failed,#27AE60)]' : '[background:var(--Failed,#FA4D56)]'} pl-3 pr-1.5 py-1`} /> */}
        <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${
          isGroudTrueGreen ? '[background:var(--Failed,#27AE60)]' : '[background:var(--Failed,#FA4D56)]'
        } pl-3 pr-1.5 py-1`} />
        <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${latestPassStatus === 'empty'
            ? 'border border-[color:var(--default-Gray-3,#828282)]'
            : (latestPassStatus
              ?
                '[background:var(--Failed,#27AE60)]'
              :
                '[background:var(--Failed,#FA4D56)]'
              ) } pl-3 pr-1.5 py-1`}  />
      </div>

      {/* border border-[color:var(--default-Gray-3,#828282)] */}

      <div className='flex  py-1 px-2 items-center justify-center gap-1 self-stretch w-[136px]'>
        {_.get(selectedLineItemResult, '[0].training_example', false) && (
          <img
            src='/icn/star_yellow.svg'
            alt='star'
            className='w-[10px] h-[10px]'
          />
        )}
        <span className={`font-source text-[10px] font-normal leading-[150%] tracking-[normal]
          ${latestPassStatus === 'empty'
            ? 'text-[#fff]'
            : (latestPassStatus
              ?
                'text-[#6FCF97]'
              :
                'text-[#FF6A6A]'
              )
          }`}>
          {_.isInteger(_.get(selectedLineItemResult, '[0].array_index', null)) && `${t('common.subBoard')}${_.get(selectedLineItemResult, '[0].array_index', '') + 1}.`}
          {(() => {
            const featureType = _.get(selectedLineItemResult, '[0].feature_type', '');
            return featureType.startsWith('_text') ? '_text' : featureType;
          })()}
        </span>
      </div>
    </div>
  );
};

const SelectedFeatureErrorInfo = (props) => {
  const {
    selectedLineItemResult,
    selectedDetail,
    setSelectedDetail,
    trainingSetSelectedErrorType,
    setTrainingSetSelectedErrorType,
    handleRefetch,
    feedbackErrorType,
    selectedFeatureType,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    passedOnly,
    componentListGroupMode,
    selectedCid,
    selectedScope,
    selectedGroupFeatureTypeAgentParams,
    allFeatures,
    selectedArrayIndex,
    selectedFid,
    selectedLineItemResultKey,
    onMaskChange,
    setFeedbackErrorType,
    allComponents,
    setShouldTrainingSetRefetch,
    setShouldDatasetInComponentListRefetch,
  } = props;

  // console.log('selectedLineItemResult', selectedLineItemResult);

  const { t } = useTranslation();

  const [details, setDetails] = useState(null);
  const [curDisplayedErrorInfo, setCurDisplayedErrorInfo] = useState(null);
  const [selectedRCid, setSelectedRCid] = useState(null);
  const [selectedDetailsAgentResult, setSelectedDetailsAgentResult] = useState(null);
  const [selectedRPid, setSelectedRPid] = useState(null);
  const [selectedAgentParsedErrorDetail, setSelectedAgentParsedErrorDetail] = useState(null);
  const [currentParsedErrorInfo, setCurrentParsedErrorInfo] = useState(null);
  const [errorType, setErrorType] = useState(null);
  const [reevalErrorType, setReevalErrorType] = useState(null);
  const [shouldAddToFuzzyMapTriggerDisplayed, setShouldAddToFuzzyMapTriggerDisplayed] = useState(false);
  // const [feedbackErrorType, setFeedbackErrorType] = useState(null);

  const [annotateGroup] = useAnnotateGroupMutation();
  const [lazyGetInspectedComponent] = useLazyGetInspectedComponentQuery();
  const [cancelFeedback] = useCancelFeedbackMutation();
  const [cancelAnnotateGroup] = useCancelAnnotateGroupMutation();
  const [lazyGetDataExample] = useLazyGetDataSetExampleQuery();
  const [lazyGetConveyorInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [retrainTrigger] = useModelUpdateTriggerMutation();
  
  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  useEffect(() => {
    if (_.isEmpty(selectedLineItemResult)) return;

    const newDetails = [];
    let firstDetailAgentResult;
    let firstPid;

    for (const a of selectedLineItemResult) {
      if (
        (selectedFeatureType === leadGapFeatureType &&
          _.get(a, 'detail') !== leadInspection2DBase) ||
        (isAOI2DSMT &&
          [mountingInspection3D, leadInspection3D, solderInspection3D].includes(
            _.get(a, 'detail')
          ))
      ) {
        continue;
      }

      const d = { name: _.get(a, 'detail', null) };
      if (selectedFeatureType === leadGapFeatureType && a.detail === leadInspection2DBase && !_.isEmpty(a.reevaluation_result)) {
        const parsedError = JSON.parse(_.get(a, 'reevaluation_result.error', '{}'));
        d.reevaluatePass = _.get(parsedError, 'bridge_pass', false);
      } else if (selectedFeatureType === leadFeatureType && a.detail === leadInspection2DBase && !_.isEmpty(a.reevaluation_result)) {
        const parsedError = JSON.parse(_.get(a, 'reevaluation_result.error', '{}'));
        d.reevaluatePass = _.get(parsedError, 'lead_pass', false);
      } else {
        if (_.isEmpty(a.reevaluation_result)) {
          d.reevaluatePass = null;
        } else {
          d.reevaluatePass = _.get(a, 'reevaluation_result.pass', false);
        }
      }
      newDetails.push(d);
      if (_.isEmpty(firstDetailAgentResult)) {
        firstDetailAgentResult = a;
        // firstPid = _.split(_.get(selectedLineItemResult, 'line_item_result_key', ''), '_')[0];
        firstPid = _.get(a, 'product_id', null);
      }
    }

    setSelectedRCid(_.get(firstDetailAgentResult, 'component_id', -1));
    setSelectedDetail(_.get(firstDetailAgentResult, 'detail', ''));
    setSelectedDetailsAgentResult(firstDetailAgentResult);
    setSelectedRPid(firstPid);
    setDetails(newDetails);
  }, [
    selectedLineItemResult,
    selectedFeatureType,
  ]);

  useEffect(() => {
    if (!selectedDetailsAgentResult) return;

    let currentErrorDetail;

    setErrorType(_.get(selectedDetailsAgentResult, 'error_type', ''));
    if (_.isEmpty(selectedDetailsAgentResult.reevaluation_result)) {
      setReevalErrorType(_.get(selectedDetailsAgentResult, 'reevaluation_result.error_type', ''));
    }

    // console.log('selectedDetailsAgentResult', selectedDetailsAgentResult);

    if (_.isEmpty(selectedDetailsAgentResult.reevaluation_result)) {
      currentErrorDetail = _.get(JSON.parse(_.get(selectedDetailsAgentResult, 'error', '{}')), 'error_detail', {});
    } else {
      currentErrorDetail = JSON.parse(_.get(selectedDetailsAgentResult, 'reevaluation_result.error', {}));
    }

    const parsedError = {};
    let mask = null;

    // console.log('selectedDetailsAgentResult', selectedDetailsAgentResult);
    // console.log('selectedGroupFeatureTypeAgentParams', selectedGroupFeatureTypeAgentParams);

    for (const resultErrorType of _.keys(currentErrorDetail)) {
      let referenceLineItemParamName = _.get(systemMetadata, `score_to_param_map.[${selectedDetailsAgentResult.detail}/${resultErrorType}]`, null);
      referenceLineItemParamName = _.split(referenceLineItemParamName, '/')[1];
      const param = _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${referenceLineItemParamName}`);
      // console.log('param', param);
      let paramTypeAndValue;
      if (resultErrorType === filletVolumeRatio) {
        // console.log('selectedDetailsAgentResult', selectedDetailsAgentResult);
        // console.log('selectedGroupFeatureTypeAgentParams', selectedGroupFeatureTypeAgentParams);
        // console.log('filletUpperThreshold', filletUpperThreshold);

        // one result refers to three reference
        paramTypeAndValue = {
          type: filletVolumeRatio,
          filletUpperThreshold: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletUpperThreshold}.param_float.value`, null),
          filletLowerThreshold: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletLowerThreshold}.param_float.value`, null),
          filletOpenThreshold: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletOpenThreshold}.param_float.value`, null),
          filletMinThreshold: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletLowerThreshold}.param_float.min`, null),
          filletMaxThreshold: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletUpperThreshold}.param_float.max`, null),
        };
      } else if (resultErrorType === validRatioList && selectedDetailsAgentResult.detail === solderInspection2D) {
        paramTypeAndValue = {
          type: validRatioList,
          list: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${solder2DValidRatioRanges}.param_vector`, []),
        };
      } else if (
        selectedDetailsAgentResult.detail === leadInspection2D &&
        [solderValidRatioList, padValidRatioList, tipValidRatioList].includes(resultErrorType)
      ) {
        paramTypeAndValue = {
          type: resultErrorType,
          mapValidRangeMap: {
            solder: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${solderValidRatioRange}.param_range`, {}),
            liftedLead: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${selectedDetailsAgentResult.detail}.params.${liftedLeadTipValidRatioRange}.param_range`, {}),
          },
        };
      } else {
        paramTypeAndValue = getAgentParamTypeNValueByParam(
          param,
          selectedDetailsAgentResult.detail,
          referenceLineItemParamName,
        );
      }

      // console.log('paramTypeAndValue', paramTypeAndValue);

      const rawInference = _.get(currentErrorDetail, `${resultErrorType}`, null);
      let inferenceResult = rawInference;
      let wrongPositions = [];
      let groupEquivalence = [];
      let reverseExpectedText = '';
      let selectedNormal = null;
      let reversePredictedText = '';
      // console.log('currentErrorDetail', currentErrorDetail);
      if (_.has(currentErrorDetail, 'predicted_text')) {
        inferenceResult = _.get(currentErrorDetail, 'predicted_text', '');
        paramTypeAndValue = {
          ...paramTypeAndValue,
          value: _.get(currentErrorDetail, 'expected_text', _.get(paramTypeAndValue, 'value')),
        };
        wrongPositions = _.get(currentErrorDetail, 'wrong_positions', []);
        groupEquivalence = _.get(currentErrorDetail, 'group_equivalence', []);
        reverseExpectedText = _.get(currentErrorDetail, 'expected_text_reverse', '');
        selectedNormal = _.get(currentErrorDetail, 'selected_normal', null);
        reversePredictedText = _.get(currentErrorDetail, 'predicted_text_reverse', '');
      }

      if ((selectedFeatureType === leadGapFeatureType
          ? twoDLeadGapAgentParamCheck(selectedDetailsAgentResult.detail, referenceLineItemParamName)
          : true) &&
        (selectedFeatureType === leadFeatureType
          // ? referenceLineItemParamName !== 'bridge_threshold'
          ? !_.includes(agentParamStatsList[leadGapFeatureType][leadInspection2DBase], referenceLineItemParamName)
          : true)) {
        parsedError[`${resultErrorType}`] = {
          reference: paramTypeAndValue,
          inferenceResult,
          referenceAgentParamName: referenceLineItemParamName,
          detail: _.get(selectedDetailsAgentResult, 'detail', ''),
          wrongPositions,
          groupEquivalence,
          reverseExpectedText,
          selectedNormal,
          reversePredictedText,
        };
      }
    }
    if (selectedDetailsAgentResult.detail === leadInspection2D) {
      mask = {
        solder: _.map(_.get(currentErrorDetail, 'solder_components', []), sc => ({ image: sc.mask_image, roi: sc.roi })),
        pad: _.map(_.get(currentErrorDetail, 'pad_components', []), pc => ({ image: pc.mask_image, roi: pc.roi })),
        tip: _.map(_.get(currentErrorDetail, 'tip_components', []), tc => ({ image: tc.mask_image, roi: tc.roi })),
      };
    } else {
      mask = _.get(currentErrorDetail, 'result_image_jpeg', null);
    }

    setCurrentParsedErrorInfo({
      parsedError,
      mask,
    });
  }, [
    selectedDetailsAgentResult,
    selectedGroupFeatureTypeAgentParams,
    selectedFeatureType,
  ]);

  useEffect(() => {
    if (!_.startsWith(selectedFeatureType, '_text') || !_.isInteger(selectedCid)) {
      setShouldAddToFuzzyMapTriggerDisplayed(false);
      return;
    }

    const run = async (
      selectedCid,
      goldenProductId,
    ) => {
      const payload = {
        golden_product_id: Number(goldenProductId),
        step: 0,
        has_feedback: true,
        feature_type: textVerification,
        component_id: selectedCid,
      };

      const res = await lazyGetDataExample(payload);

      if (res.error) {
        aoiAlert(t('notification.error.getDataExample'), ALERT_TYPES.COMMON_ERROR);
        console.error('getDataExample error:', _.get(res, 'error.message', ''));
        setShouldAddToFuzzyMapTriggerDisplayed(false);
        return;
      }

      // search for pass = false and training_example = true
      for (const r of res.data.line_item_results) {
        if (
          !_.isEmpty(_.get(r, 'reevaluation_result', {})) &&
          _.get(r, 'feature_type', '') === selectedFeatureType &&
          !_.get(r, 'reevaluation_result.pass', false) &&
          _.get(r, 'training_example', false)
        ) {
          setShouldAddToFuzzyMapTriggerDisplayed(true);
          return;
        }
      }
      setShouldAddToFuzzyMapTriggerDisplayed(false);
    };

    run(selectedCid, goldenProductId);
  }, [
    selectedCid,
    selectedFeatureType,
  ]);

  if (_.isEmpty(selectedLineItemResult)) return null;

  return (
    <Fragment>
      <div className='flex px-1 pt-1 items-center self-stretch gap-2'>
        <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px] text-gray-4'>
          {t(`featureTypeDisplayText.${(() => {
            const featureType = _.get(selectedLineItemResult, '[0].feature_type', '');
            return featureType.startsWith('_text') ? '_text' : featureType;
          })()}`)}
        </span>
        <Select
          size='small'
          // can provide feedback nor the in training example
          disabled={_.get(selectedLineItemResult, '[0].training_example', false) || feedbackErrorType === null}
          options={_.map(_.keys(_.get(systemMetadata, 'error_type_to_code', {})), (err) => ({
            value: err,
            label:
              <div className='flex items-center gap-1 h-[26px]'>
                {/* {!_.isEmpty(errorType) && errorType !== 'no_error' && errorType === err &&
                  <img
                    src='/icn/warning_red.svg'
                    alt='warning'
                    className='w-[12px] h-[12px]'
                  />
                }
                {!_.isEmpty(errorType) && errorType === 'no_error' && errorType === err &&
                  <img
                    src='/icn/check_green.svg'
                    alt='check'
                    className='w-[12px] h-[12px]'
                  />
                } */}
                {!_.isEmpty(feedbackErrorType) && feedbackErrorType === err && feedbackErrorType !== 'no_error' && (
                  <img
                    src='/icn/thumbdown_red.svg'
                    alt='thumbdown'
                    className='w-[12px] h-[12px]'
                  />
                )}
                {!_.isEmpty(feedbackErrorType) && feedbackErrorType === err && feedbackErrorType === 'no_error'&& (
                  <img
                    src='/icn/thumbup_green.svg'
                    alt='thumbup'
                    className='w-[12px] h-[12px]'
                  />
                )}
                {/* <span
                  className={`font-source text-[12px] font-normal leading-[150%] pt-0.5 ${errorType === err && errorType !== 'no_error' && 'text-[#EB5757]'}`}
                >
                  {t(`inferenceErrorType.${err}`)}
                </span> */}
                <span
                  className={`font-source text-[12px] font-normal leading-[150%] pt-0.5 `}
                >
                  {t(`inferenceErrorType.${err}`)}
                </span>
              </div>,
            disabled: err === variantComponentErrorType,
          }))}
          value={feedbackErrorType}
          onChange={(value) => {
            if (value === variantComponentErrorType) return;
            const run = async (selectedDetailsAgentResult, selectedRPid,
              goldenProductId,
              passedOnly,
              selectedFeatureType,
              selectedCid,
              selectedScope,
              selectedPartNo,
              selectedPackageNo,
              allFeatures,
              arrayIndex,
              selectedFid,
              allComponents,
            ) => {
              const res = await annotateGroup({
                product_id: Number(selectedRPid),
                group_id: _.get(selectedDetailsAgentResult, 'component_id', -1),
                step: 0,
                error_type: value,
                array_index: arrayIndex,
              });

              if (res.error) {
                aoiAlert(t('notification.error.annotateGroup'), ALERT_TYPES.COMMON_ERROR);
                console.error('annotateGroup error:', _.get(res, 'error.message', ''));
                return;
              }

              setShouldTrainingSetRefetch(true);
              setShouldDatasetInComponentListRefetch(true);

              // TODO: refetch...
              await handleRefetch(
                goldenProductId,
                passedOnly,
                selectedFeatureType,
                selectedCid,
                selectedScope,
                selectedPartNo,
                selectedPackageNo,
                allFeatures,
                selectedFid,
                arrayIndex,
                false,
                allComponents,
              );

              const run = async (ipcProductId, rcid) => {
                const res = await lazyGetInspectedComponent({
                  inspected_product_id: ipcProductId,
                  step: 0,
                }, false);
          
                if (res.error) {
                  aoiAlert(t('notification.error.getInspectedComponent'), ALERT_TYPES.COMMON_ERROR);
                  console.error('getInspectedComponent error:', _.get(res, 'error.message', ''));
                  return;
                }
          
                const componentInferenceResult = _.find(res.data, i => i.result_component_id === rcid);
                setFeedbackErrorType(_.get(componentInferenceResult, 'feedback_error_type', null));
              };

              run(selectedRPid, _.get(selectedDetailsAgentResult, 'component_id', -1));
            };

            run(selectedDetailsAgentResult, selectedRPid,
              goldenProductId,
              passedOnly,
              selectedFeatureType,
              selectedCid,
              selectedScope,
              selectedPartNo,
              selectedPackageNo,
              allFeatures,
              selectedArrayIndex,
              selectedFid,
              allComponents,
            );
          }}
          popupMatchSelectWidth={false}
          style={{ width: '120px' }}
        />
        <Tooltip
          title={
            <div className='flex flex-col gap-1 self-stretch'>
              {/* <div className='flex items-center gap-2'>
                <img
                  src='/icn/check_green.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.checkerDesc')}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/warning_red.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.warningDesc')}
                </span>
              </div> */}
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/thumbup_green.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.thumbsUpDesc')}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/thumbdown_red.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.thumbsDownDesc')}
                </span>
              </div>
            </div>
          }
        >
          <div className='flex items-center justify-center w-[16px] h-[16px]'>
            <img
              src='/icn/info_white.svg'
              alt='info'
              className='w-[12px] h-[12px]'
            />
          </div>
        </Tooltip>
        {!_.get(selectedLineItemResult, '[0].training_example', false) && !_.isEmpty(feedbackErrorType) &&
          <Button
            size='small'
            onClick={() => {
              // console.log('selectedLineItemResult', selectedLineItemResult);
              const run = async (
                selectedLineItemResult,
                goldenProductId,
                passedOnly,
                selectedFeatureType,
                selectedCid,
                selectedScope,
                selectedPartNo,
                selectedPackageNo,
                allFeatures,
                selectedFid,
                selectedArrayIndex,
                allComponents,
              ) => {
                // for (const a of selectedLineItemResult) {
                //   if (!_.isEmpty(a.feedback)) {
                //     const params = {
                //       inspection_result_id: _.get(a, 'inspection_result_id', null),
                //     };
                  
                //     const res = await cancelFeedback(params);
                  
                //     if (res.error) {
                //       aoiAlert(t('notification.error.cancelFeedback'), ALERT_TYPES.COMMON_ERROR);
                //       console.error('cancelFeedback error:', _.get(res, 'error.message', ''));
                //       return;
                //     }
                //   }
                // }

                const res = await cancelAnnotateGroup({
                  product_id: _.get(selectedLineItemResult, '[0].product_id', null),
                  group_id: _.get(selectedLineItemResult, '[0].component_id', null),
                  step: _.get(selectedLineItemResult, '[0].step', null),
                  array_index: _.get(selectedLineItemResult, '[0].array_index', null),
                });

                if (res.error) {
                  aoiAlert(t('notification.error.cancelFeedback'), ALERT_TYPES.COMMON_ERROR);
                  console.error('cancelAnnotateGroup error:', _.get(res, 'error.message', ''));
                  return;
                }

                // refetch
                await handleRefetch(
                  goldenProductId,
                  passedOnly,
                  selectedFeatureType,
                  selectedCid,
                  selectedScope,
                  selectedPartNo,
                  selectedPackageNo,
                  allFeatures,
                  selectedFid,
                  selectedArrayIndex,
                  false,
                  allComponents,
                );
              };

              run(
                selectedLineItemResult,
                goldenProductId,
                passedOnly,
                selectedFeatureType,
                selectedCid,
                selectedScope,
                selectedPartNo,
                selectedPackageNo,
                allFeatures,
                selectedFid,
                selectedArrayIndex,
                allComponents,
              );
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.cancelFeedback')}
            </span>
          </Button>
        }
      </div>
      <SmallTabsConfigProvider>
        <Tabs
          type='card'
          items={
            _.map(details, (detail) => {
              let color = '';
              if (detail.reevaluatePass === null) {
                color = 'text-[#fff]';
              } else {
                color = detail.reevaluatePass ? 'text-[#57F2C4]' : 'text-[#EB5757]';
              }
              return {
                key: detail.name,
                label: <span className={`font-source text-[12px] font-normal leading-[150%] ${color}`}>
                  {t(`lineItemName.${detail.name}`)}
                </span>,
              };
            })
          }
          activeKey={_.get(selectedDetailsAgentResult, 'detail', '')}
          onChange={(key) => {
            // setSelectedDetailsAgentResult(_.find(_.get(selectedLineItemResult, 'agentResults'), r => r.detail === key));
            setSelectedDetailsAgentResult(_.find(selectedLineItemResult, r => r.detail === key));
          }}
        />
      </SmallTabsConfigProvider>

      {!_.isEmpty(errorType) && 
        <div className='flex items-center gap-2 self-stretch px-1 py-1'>
          <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
            {t('productDefine.predictedErrorType')}: {t(`inferenceErrorType.${_.isEmpty(errorType) ? 'no_error' : errorType}`)}
          </span>
        </div>
      }
      {!_.isEmpty(reevalErrorType) &&
        <div className='flex items-center gap-2 self-stretch px-1 py-1'>
          <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
            {t('productDefine.reevaluateErrorType')}: {t(`inferenceErrorType.${_.isEmpty(reevalErrorType) ? 'no_error' : reevalErrorType}`)}
          </span>
        </div>
      }
      <div className='flex flex-1 self-stretch px-2 py-1 gap-2'>
        {!_.isEmpty(currentParsedErrorInfo) &&
          <InferenceResult
            featureError={currentParsedErrorInfo}
            maskList={currentParsedErrorInfo.mask}
            onMaskChange={(info) => onMaskChange(info, selectedLineItemResultKey, _.get(selectedLineItemResult, '[0]', {}))}
            isTraingSet={true}
          />
        }
      </div>
      {_.startsWith(selectedFeatureType, '_text') && shouldAddToFuzzyMapTriggerDisplayed &&
        <Button
          onClick={() => {
            const run = async (goldenProductId) => {
							const runGoldenIds = await getRunningTaskGoldenProductIds(lazyGetConveyorInferenceStatus);
					
							if (_.includes(runGoldenIds, Number(goldenProductId))) {
								dispatch(setIsContainerLvlLoadingEnabled(false));
								dispatch(setContainerLvlLoadingMsg(''));
								aoiAlert(t('notification.error.pleaseStopAllInspectionTaskRelatedToThisProduct'), ALERT_TYPES.COMMON_ERROR);
								return;
							}

							const res = await retrainTrigger({
								model_types: [
									modelTypes.textVerificationModel,
								],
								golden_product_id: Number(goldenProductId),
								update_parameters: true,
							});

							await sleep(3 * 1000); // ensure task schuedule is written in db
					
							if (res.error) {
								aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
								console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
								dispatch(setIsContainerLvlLoadingEnabled(false));
								dispatch(setContainerLvlLoadingMsg(''));
								return false;
							}
					
							dispatch(setIsTrainingRunning(true));
							dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
						};

						run(goldenProductId);
          }}
        >
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('productDefine.addToFuzzyMap')}
          </span>
        </Button>
      }
    </Fragment>
  );
};

export default GroupTrainingSet;